<template>
  <div class="course-page">
    <!-- 头部 -->
    <van-nav-bar title="全部课程" fixed style="background-color: #1bb394" />

    <!-- 内容区域 -->
    <div class="content">
      <!-- 课程列表 -->
      <div class="course-list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <div
              v-for="course in courseList"
              :key="course.id"
              class="course-item"
              :style="{
                display:
                  course.assessment_video?.videos?.length > 0
                    ? 'block'
                    : 'none',
              }"
            >
              <div class="course-header">
                <div class="course-title">
                  {{ course.assessment_video.title }}
                </div>
                <van-button
                  type="primary"
                  size="small"
                  text="开始"
                  class="start-btn"
                  @click="startCourse(course)"
                ></van-button>
              </div>
              <div class="course-info">
                <span class="course-category">{{
                  course.assessment_video.category.name
                }}</span>
                <span class="course-time">{{
                  formatTime(course.created_at)
                }}</span>
                <span class="course-duration"
                  >共
                  {{ course.assessment_video?.videos?.length || 0 }} 课时</span
                >
              </div>
              <div class="teacher-list">
                <div
                  v-for="teacher in course.assessment_video.teachers"
                  :key="teacher.teacher.id"
                  class="teacher-avatar"
                >
                  <van-image
                    :src="teacher.teacher.avatar"
                    width="24"
                    height="24"
                    round
                    fit="cover"
                  />
                </div>
                <div class="teacher-names">
                  <span
                    v-for="teacher in course.assessment_video.teachers"
                    :key="teacher.teacher.id"
                  >
                    {{ teacher.teacher.name }}
                  </span>
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { getUserCourseList } from "@/api/user";
import type { AssessmentRecord } from "@/types/course";
import { formatTime } from "@/utils/functions";
import type { pageInfoType } from "@/types/common";
const authStore = useAuthStore();
const router = useRouter();
const refreshing = ref(false);
const loading = ref(false);
const finished = ref(false);
const pageInfo: pageInfoType = {
  page: 1,
  total: 0,
  pageSize: 10,
  pages: 0,
};
let courseList = ref<AssessmentRecord[]>([]);
const renderAllCourse = async () => {
  const response = await getUserCourseList(pageInfo);
  if (response.code == 200) {
    courseList.value = response.data;
    pageInfo.total = response.page.total;
    pageInfo.pages = response.page.pages;
  }
};

// 下拉刷新
const onRefresh = async () => {
  pageInfo.page = 1;
  refreshing.value = true;
  await renderAllCourse();
  refreshing.value = false;
};

// 加载更多
const onLoad = async () => {
  // 这里可以实现分页加载
  if (pageInfo.page >= pageInfo.pages) {
    finished.value = true;
    return;
  }
  if (loading.value == true) {
    return;
  }
  loading.value = true;
  pageInfo.page += 1;
  await renderAllCourse();
  loading.value = false;
};

// 开始课程
const startCourse = (course: AssessmentRecord) => {
  router.push(`/course/play/${course.id}`);
};

onMounted(async () => {
  await authStore.login();
  renderAllCourse();
});
</script>

<style scoped>
.course-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 50px;
}
:deep(.van-nav-bar__title) {
  color: #fff;
}
.content {
  padding-top: 46px;
}

.course-list {
  padding: 12px;
}

.course-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.course-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
  margin-right: 12px;
}

.start-btn {
  background-color: #1bb394;
  border-color: #1bb394;
  border-radius: 16px;
  padding: 4px 16px;
  padding-top: 6px;
  font-size: 12px;
}

.course-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: #999;
}

.course-category {
  color: #1bb394;
  margin-right: 12px;
}

.course-time {
  margin-right: 12px;
}

.teacher-list {
  display: flex;
  align-items: center;
}

.teacher-avatar {
  margin-right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.teacher-names {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}
</style>
