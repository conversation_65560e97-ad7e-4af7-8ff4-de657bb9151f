package h5

import (
	"ai_select_admin/config"
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/services"
	"ai_select_admin/utils"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

type User struct {
}

type loginCode struct {
	Code string
}

type UpdateVideoProgressRequest struct {
	AssessmentRecordID uint `json:"assessment_record_id" binding:"required"`
	VideoID            uint `json:"video_id" binding:"required"`
	Progress           int  `json:"progress" binding:"required,min=0,max=100"`
	Duration           int  `json:"duration" binding:"required,min=0"`
}

// ExamPaperResponse 试卷响应结构
type ExamPaperResponse struct {
	ID                 uint       `json:"id"`
	Title              string     `json:"title"`
	Description        string     `json:"description"`
	Type               int        `json:"type"`
	Duration           int        `json:"duration"`
	TotalScore         int        `json:"total_score"`
	PassScore          int        `json:"pass_score"`
	QuestionCount      int        `json:"question_count"`
	CategoryName       string     `json:"category_name"`
	AssessmentTitle    string     `json:"assessment_title"` // 考核标题
	CreatedAt          time.Time  `json:"created_at"`
	UpdatedAt          time.Time  `json:"updated_at"`
	AssessmentRecordID uint       `json:"assessment_record_id"`
	ExamStatus         int        `json:"exam_status"`
	ExamScore          *int       `json:"exam_score"`
	ExamStartTime      *time.Time `json:"exam_start_time"`
}

// ExamListResponse 试卷列表响应结构
type ExamListResponse struct {
	ExamPapers     []ExamPaperResponse `json:"exam_papers"`
	PracticePapers []ExamPaperResponse `json:"practice_papers"`
}

func (u *User) Login(c *gin.Context) {
	var req loginCode
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误")
		return
	}
	var config = services.WechatConfig{
		CorpID:  config.AppConfig.CopConfig.CorpId,
		Secret:  config.AppConfig.CopConfig.CorpSecret,
		AgentID: config.AppConfig.CopConfig.AgentId,
	}
	WechatService := services.NewWechatService(&config)
	Data, err := WechatService.GetDetailUserInfo(req.Code)
	if err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	weComUserInfo, err := WechatService.FindUserDetail(Data.Userid)

	if err != nil {
		logger.Errorf("获取员工详情失败:uid=%s\n", Data.Userid)
		utils.Error(c, 500, err.Error())
	}
	employeeModel := &models.Employee{}

	result := database.DB.Model(&models.Employee{}).First(&employeeModel, "user_id=?", Data.Userid)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			//员工不存在 直接更新
			//获取员工详细信息
			err = copier.Copy(employeeModel, weComUserInfo)
			employeeModel.Name = weComUserInfo.Name
			employeeModel.UserID = weComUserInfo.UserID
			employeeModel.MainDeptID = weComUserInfo.MainDepartment
			isLeaderInDeptBytes, _ := json.Marshal(weComUserInfo.IsLeaderInDept)
			employeeModel.IsLeaderInDept = string(isLeaderInDeptBytes)
			departmentBytes, _ := json.Marshal(weComUserInfo.Department)
			employeeModel.Department = string(departmentBytes)
			orderBytes, _ := json.Marshal(weComUserInfo.Order)
			employeeModel.Order = string(orderBytes)
			employeeModel.Gender = Data.Gender
			employeeModel.Avatar = Data.Avatar
			employeeModel.Mobile = Data.Mobile
			employeeModel.Email = Data.Email
			employeeModel.Address = Data.Address
			if err != nil {
				utils.Error(c, 500, err.Error())
				return
			}
			departments := []models.Department{}
			database.DB.Model(models.Department{}).Where("wechat_id in ?", weComUserInfo.Department).Find(&departments)
			if len(departments) > 0 {
				logger.Infof("获取到的部门信息是%+v\n", departments)
				employeeModel.Departments = departments
			}
			//添加记录
			result = database.DB.Create(employeeModel)
			if result.Error != nil {
				logger.Errorf("添加用户失败:uid=%s,reason:%s\n", Data.Userid, result.Error.Error())
				utils.Error(c, 500, result.Error.Error())
				return
			}
		} else {
			logger.Errorf("查询失败:uid=%s\n", Data.Userid)
			utils.Error(c, 500, result.Error.Error())
		}
	}

	employeeModel.Gender = Data.Gender
	employeeModel.Avatar = Data.Avatar
	employeeModel.Mobile = Data.Mobile
	employeeModel.Email = Data.Email
	employeeModel.Address = Data.Address
	//获取成功更新用户信息到 数据表
	result = database.DB.Model(&models.Employee{}).Where("user_id=?", Data.Userid).Updates(employeeModel)
	if result.Error != nil {
		utils.ErrorWithData(c, 500, "更新失败", result.Error.Error())
	}
	result = database.DB.Model(&models.Employee{}).First(&employeeModel, "user_id=?", Data.Userid)

	if result.Error != nil {
		logger.Errorf("查询失败:uid=%s\n", Data.Userid)
		utils.Error(c, 500, result.Error.Error())
	}
	token, err := utils.GenerateToken(
		employeeModel.ID,
		employeeModel.Name,
		"user",
	)
	if err != nil {
		utils.InternalServerError(c, err.Error())
	}
	utils.SuccessWithMsg(
		c,
		"登陆成功",
		token,
	)
}
func (u *User) GetUserInfo(c *gin.Context) {
	uid, _ := c.Get("user_id")
	var employee = &models.Employee{}
	result := database.DB.Preload("Departments").First(employee, uid)
	if result.Error != nil {
		utils.Error(c, 500, "获取用户信息失败")
	}
	logger.Infof("获取用户成功%+v\n", employee)
	utils.Success(c, employee)
}
func (u *User) GetCourseList(c *gin.Context) {
	uid, _ := c.Get("user_id")
	pageStr := c.Query("page")
	page, _ := strconv.Atoi(pageStr)
	pageSizeStr := c.Query("pageSize")
	pageSize, _ := strconv.Atoi(pageSizeStr)
	offset := (page - 1) * pageSize

	var total int64
	pageInfo := utils.PageInfo{}
	database.DB.Model(&models.AssessmentRecord{}).Where("employee_id=? AND deleted_at IS NULL", uid).Count(&total)
	assessmentRecord := []models.AssessmentRecord{}
	err := database.DB.Preload("AssessmentVideo.Category").Preload("AssessmentVideo.Videos").Preload("AssessmentVideo.Teachers.Teacher").Where("employee_id=?", uid).Limit(pageSize).Offset(offset).Find(&assessmentRecord).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		utils.Success(c, []interface{}{})
		utils.PageSuccess(c, []interface{}{}, pageInfo)
		return
	}
	pageInfo.Current = page
	pageInfo.Pages = int(total) % pageSize
	pageInfo.Size = pageSize
	pageInfo.Total = total
	utils.PageSuccess(c, assessmentRecord, pageInfo)
}

// GetCourseDetail 获取课程详情（包含视频列表）
func (u *User) GetCourseDetail(c *gin.Context) {
	uid, _ := c.Get("user_id")
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的课程ID")
		return
	}

	var assessmentRecord models.AssessmentRecord
	err = database.DB.
		Preload("AssessmentVideo.Category").
		Preload("AssessmentVideo.Videos.Video").
		Preload("AssessmentVideo.Teachers.Teacher").
		Where("id = ? AND employee_id = ? AND deleted_at IS NULL", uint(id), uid).
		First(&assessmentRecord).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			utils.NotFound(c, "课程不存在或无权限访问")
		} else {
			logger.Errorf("获取课程详情失败: %v", err)
			utils.InternalServerError(c, "获取课程详情失败")
		}
		return
	}

	utils.Success(c, assessmentRecord)
}

// GetExamList 获取用户可参与的试卷列表
func (u *User) GetExamList(c *gin.Context) {
	uid, _ := c.Get("user_id")
	logger.Infof("获取用户 %v 的试卷列表", uid)

	// 初始化响应数据
	response := ExamListResponse{
		ExamPapers:     make([]ExamPaperResponse, 0),
		PracticePapers: make([]ExamPaperResponse, 0),
	}

	// 移除测试数据，使用真实数据

	// 获取用户的考核记录，包含关联的试卷信息
	var assessmentRecords []models.AssessmentRecord
	err := database.DB.
		Preload("AssessmentVideo.Exam.Questions").
		Preload("AssessmentVideo.Category").
		Preload("AssessmentVideo.PracticeExams.Exam.Questions").
		Where("employee_id = ? AND deleted_at IS NULL", uid).
		Find(&assessmentRecords).Error

	if err != nil {
		logger.Errorf("查询用户考核记录失败: %v", err)
		// 即使查询失败，也返回空数据而不是错误
		utils.Success(c, response)
		return
	}

	logger.Infof("用户 %v 的考核记录数量: %d\n", uid, len(assessmentRecords))

	// 构建试卷列表响应，初始化为空数组而不是nil
	examPapers := make([]ExamPaperResponse, 0)
	practicePapers := make([]ExamPaperResponse, 0)

	for _, record := range assessmentRecords {
		categoryName := ""
		if record.AssessmentVideo.Category != nil {
			categoryName = record.AssessmentVideo.Category.Name
		}

		// 获取考核标题
		assessmentTitle := ""
		if record.AssessmentVideo != nil {
			assessmentTitle = record.AssessmentVideo.Title
		}

		// 处理考试试卷（主试卷）
		if record.AssessmentVideo.Exam != nil {
			exam := record.AssessmentVideo.Exam

			paper := ExamPaperResponse{
				ID:              exam.ID,
				Title:           exam.Title,
				Description:     exam.Description,
				Type:            exam.Type,
				Duration:        exam.Duration,
				TotalScore:      exam.TotalScore,
				PassScore:       exam.PassScore,
				QuestionCount:   len(exam.Questions),
				CategoryName:    categoryName,
				AssessmentTitle: assessmentTitle, // 添加考核标题
				CreatedAt:       exam.CreatedAt,
				UpdatedAt:       exam.UpdatedAt,
				// 考核记录相关信息
				AssessmentRecordID: record.ID,
				ExamStatus: func() int {
					if record.ExamScore != nil {
						return 1
					} else {
						return 0
					}
				}(),
				ExamScore:     record.ExamScore,
				ExamStartTime: record.ExamStartTime,
			}

			// 根据试卷类型分类
			if exam.Type == 1 { // 考试试卷
				examPapers = append(examPapers, paper)
			} else if exam.Type == 2 { // 刷题试卷
				practicePapers = append(practicePapers, paper)
			}
		}

		// 处理刷题试卷（关联的刷题试卷）
		for _, practiceExam := range record.AssessmentVideo.PracticeExams {
			if practiceExam.Exam != nil {
				exam := practiceExam.Exam

				paper := ExamPaperResponse{
					ID:              exam.ID,
					Title:           exam.Title,
					Description:     exam.Description,
					Type:            exam.Type,
					Duration:        exam.Duration,
					TotalScore:      exam.TotalScore,
					PassScore:       exam.PassScore,
					QuestionCount:   len(exam.Questions),
					CategoryName:    categoryName,
					AssessmentTitle: assessmentTitle, // 添加考核标题
					CreatedAt:       exam.CreatedAt,
					UpdatedAt:       exam.UpdatedAt,
					// 考核记录相关信息
					AssessmentRecordID: record.ID,
					ExamStatus:         0, // 刷题试卷默认未考试状态
					ExamScore:          nil,
				}

				// 刷题试卷都归类为练习
				practicePapers = append(practicePapers, paper)
			}
		}
	}

	response.ExamPapers = examPapers
	response.PracticePapers = practicePapers

	logger.Infof("返回试卷列表 - 考试试卷: %d, 刷题试卷: %d", len(examPapers), len(practicePapers))
	utils.Success(c, response)
}

// GetLearningStats 获取用户学习统计
func (u *User) GetLearningStats(c *gin.Context) {
	uid, _ := c.Get("user_id")

	// 获取观看视频总时长
	var totalVideoTime int64
	database.DB.Model(&models.VideoProgress{}).
		Where("employee_id = ?", uid).
		Select("COALESCE(SUM(duration), 0)").
		Scan(&totalVideoTime)

	// 获取刷题总数量（已完成的刷题试卷对应的题目数量总和）
	var practiceCount int64
	database.DB.Table("hr_exam_record").
		Select("COALESCE(SUM(hr_exam_record.total_count), 0)").
		Joins("JOIN hr_exam ON hr_exam.id = hr_exam_record.exam_id").
		Where("hr_exam_record.employee_id = ? AND hr_exam.type = 2 AND hr_exam_record.status = 2", uid).
		Scan(&practiceCount)

	// 获取考试总次数（已完成的考试试卷记录数）
	var examCount int64
	database.DB.Model(&models.ExamRecord{}).
		Joins("JOIN hr_exam ON hr_exam.id = hr_exam_record.exam_id").
		Where("hr_exam_record.employee_id = ? AND hr_exam.type = 1 AND hr_exam_record.status = 2", uid).
		Count(&examCount)

	// 获取平均分数（考试试卷的平均分）
	var avgScore float64
	database.DB.Model(&models.ExamRecord{}).
		Joins("JOIN hr_exam ON hr_exam.id = hr_exam_record.exam_id").
		Where("hr_exam_record.employee_id = ? AND hr_exam.type = 1 AND hr_exam_record.status = 2", uid).
		Select("COALESCE(AVG(score), 0)").
		Scan(&avgScore)

	stats := gin.H{
		"video_time":     totalVideoTime,
		"practice_count": practiceCount,
		"exam_count":     examCount,
		"avg_score":      avgScore,
	}

	utils.Success(c, stats)
}

// GetExamRecords 获取用户考试记录（只获取考试试卷的记录）
func (u *User) GetExamRecords(c *gin.Context) {
	uid, _ := c.Get("user_id")

	// 获取最近的考试记录
	var examRecords []models.ExamRecord
	err := database.DB.
		Preload("Exam").
		Joins("JOIN hr_exam ON hr_exam.id = hr_exam_record.exam_id").
		Where("hr_exam_record.employee_id = ? AND hr_exam.type = 1 AND hr_exam_record.status IN (2, 3)", uid).
		Order("hr_exam_record.submit_time DESC").
		Find(&examRecords).Error

	if err != nil {
		logger.Errorf("获取考试记录失败: %v", err)
		utils.InternalServerError(c, "获取考试记录失败")
		return
	}

	// 转换为响应格式
	var records []gin.H
	for _, record := range examRecords {
		examTime := ""
		if record.SubmitTime != nil {
			examTime = record.SubmitTime.Format("2006-01-02 15:04")
		}

		records = append(records, gin.H{
			"id":              record.ID,
			"title":           record.Exam.Title,
			"exam_time":       examTime,
			"score":           record.Score,
			"duration":        record.Duration / 60, // 转换为分钟
			"total_questions": record.TotalCount,
			"correct_answers": record.CorrectCount,
			"is_passed":       record.IsPassed,
		})
	}

	utils.Success(c, records)
}

// GetPracticeRecords 获取用户刷题记录（只获取刷题试卷的记录）
func (u *User) GetPracticeRecords(c *gin.Context) {
	uid, _ := c.Get("user_id")

	// 获取最近的刷题记录
	var examRecords []models.ExamRecord
	err := database.DB.
		Preload("Exam").
		Joins("JOIN hr_exam ON hr_exam.id = hr_exam_record.exam_id").
		Where("hr_exam_record.employee_id = ? AND hr_exam.type = 2 AND hr_exam_record.status IN (2, 3)", uid).
		Order("hr_exam_record.submit_time DESC").
		Find(&examRecords).Error

	if err != nil {
		logger.Errorf("获取刷题记录失败: %v", err)
		utils.InternalServerError(c, "获取刷题记录失败")
		return
	}

	// 转换为响应格式
	var records []gin.H
	for _, record := range examRecords {
		examTime := ""
		if record.SubmitTime != nil {
			examTime = record.SubmitTime.Format("2006-01-02 15:04")
		}

		records = append(records, gin.H{
			"id":              record.ID,
			"title":           record.Exam.Title,
			"exam_time":       examTime,
			"score":           record.Score,
			"duration":        record.Duration / 60, // 转换为分钟
			"total_questions": record.TotalCount,
			"correct_answers": record.CorrectCount,
			"is_passed":       record.IsPassed,
		})
	}

	utils.Success(c, records)
}

// UpdateVideoProgress 更新视频观看进度
func (u *User) UpdateVideoProgress(c *gin.Context) {
	uid, _ := c.Get("user_id")
	var req UpdateVideoProgressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证考核记录是否属于当前用户
	var assessmentRecord models.AssessmentRecord
	err := database.DB.Where("id = ? AND employee_id = ? AND deleted_at IS NULL", req.AssessmentRecordID, uid).
		First(&assessmentRecord).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			utils.NotFound(c, "考核记录不存在或无权限访问")
		} else {
			logger.Errorf("查询考核记录失败: %v", err)
			utils.InternalServerError(c, "查询考核记录失败")
		}
		return
	}

	// 验证视频是否属于该考核
	var videoRelation models.AssessmentVideoRelation
	err = database.DB.Where("assessment_video_id = ? AND video_id = ?",
		assessmentRecord.AssessmentVideoID, req.VideoID).
		First(&videoRelation).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			utils.BadRequest(c, "视频不属于该考核")
		} else {
			logger.Errorf("查询视频关联失败: %v", err)
			utils.InternalServerError(c, "查询视频关联失败")
		}
		return
	}

	// 查找或创建视频进度记录
	var videoProgress models.VideoProgress
	err = database.DB.Where("employee_id = ? AND assessment_record_id = ? AND video_id = ?",
		uid, req.AssessmentRecordID, req.VideoID).
		First(&videoProgress).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新的进度记录
			videoProgress = models.VideoProgress{
				EmployeeID:         uid.(uint),
				AssessmentRecordID: req.AssessmentRecordID,
				VideoID:            req.VideoID,
				Progress:           req.Progress,
				Duration:           req.Duration,
			}
			err = database.DB.Create(&videoProgress).Error
			if err != nil {
				logger.Errorf("创建视频进度失败: %v", err)
				utils.InternalServerError(c, "创建视频进度失败")
				return
			}
		} else {
			logger.Errorf("查询视频进度失败: %v", err)
			utils.InternalServerError(c, "查询视频进度失败")
			return
		}
	} else {
		// 更新现有进度记录
		videoProgress.Progress = req.Progress
		videoProgress.Duration = req.Duration
		err = database.DB.Save(&videoProgress).Error
		if err != nil {
			logger.Errorf("更新视频进度失败: %v", err)
			utils.InternalServerError(c, "更新视频进度失败")
			return
		}
	}

	// 更新考核记录的整体视频进度
	err = u.updateAssessmentVideoProgress(req.AssessmentRecordID)
	if err != nil {
		logger.Errorf("更新考核视频进度失败: %v", err)
		// 这里不返回错误，因为单个视频进度已经保存成功
	}

	utils.SuccessWithMsg(c, "进度更新成功", nil)
}

// updateAssessmentVideoProgress 更新考核记录的整体视频进度
func (u *User) updateAssessmentVideoProgress(assessmentRecordID uint) error {
	// 获取该考核的所有视频
	var assessmentRecord models.AssessmentRecord
	err := database.DB.Preload("AssessmentVideo.Videos").
		Where("id = ?", assessmentRecordID).
		First(&assessmentRecord).Error
	if err != nil {
		return err
	}

	totalVideos := len(assessmentRecord.AssessmentVideo.Videos)
	if totalVideos == 0 {
		return nil
	}

	// 获取所有视频的进度
	var videoProgresses []models.VideoProgress
	videoIDs := make([]uint, 0, totalVideos)
	for _, video := range assessmentRecord.AssessmentVideo.Videos {
		videoIDs = append(videoIDs, video.VideoID)
	}

	err = database.DB.Where("assessment_record_id = ? AND video_id IN ?",
		assessmentRecordID, videoIDs).
		Find(&videoProgresses).Error
	if err != nil {
		return err
	}

	// 计算平均进度
	totalProgress := 0
	for _, progress := range videoProgresses {
		totalProgress += progress.Progress
	}

	// 计算整体进度（考虑未观看的视频为0%）
	overallProgress := totalProgress / totalVideos

	// 更新考核记录的视频进度
	err = database.DB.Model(&assessmentRecord).
		Update("video_progress", overallProgress).Error
	if err != nil {
		return err
	}

	return nil
}
