package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/services"
	"ai_select_admin/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type DepartmentController struct{}

// ListDepartments 获取部门列表
func (dc *DepartmentController) ListDepartments(c *gin.Context) {
	var req models.DepartmentListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.Department{})

	// 按名称搜索
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 按父部门筛选
	if req.ParentID != nil {
		query = query.Where("parent_id = ?", *req.ParentID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取部门总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var departments []models.Department
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("sort ASC, created_at ASC").Find(&departments).Error; err != nil {
		logger.Errorf("获取部门列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var responses []*models.DepartmentResponse
	for _, dept := range departments {
		responses = append(responses, dept.ToResponse())
	}

	// 计算分页信息
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	utils.Success(c, map[string]interface{}{
		"current": req.Page,
		"size":    req.PageSize,
		"total":   total,
		"pages":   totalPages,
	})
}

// GetDepartment 获取部门详情
func (dc *DepartmentController) GetDepartment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的ID")
		return
	}

	var department models.Department
	if err := database.DB.First(&department, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "部门不存在")
		} else {
			logger.Errorf("获取部门失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, department.ToResponse())
}

// GetDepartmentTree 获取部门树形结构
func (dc *DepartmentController) GetDepartmentTree(c *gin.Context) {
	var departments []models.Department
	if err := database.DB.Where("status = ?", 1).Order("sort ASC, created_at ASC").Find(&departments).Error; err != nil {
		logger.Errorf("获取部门树失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 构建树形结构
	tree := buildDepartmentTree(departments, 0)
	utils.Success(c, tree)
}

// buildDepartmentTree 构建部门树形结构
func buildDepartmentTree(departments []models.Department, parentWechatID int) []models.DepartmentResponse {
	var tree []models.DepartmentResponse

	for _, dept := range departments {
		if dept.ParentID == parentWechatID {
			deptResp := dept.ToResponse()
			// 递归调用以构建子树
			children := buildDepartmentTree(departments, dept.WechatID)
			if len(children) > 0 {
				deptResp.Children = children
			}
			tree = append(tree, *deptResp)
		}
	}

	return tree
}

// SyncDepartments 同步企业微信部门数据
func (dc *DepartmentController) SyncDepartments(c *gin.Context) {
	// 获取默认企业微信配置
	config, err := services.GetDefaultWechatConfig()
	if err != nil {
		utils.BadRequest(c, "未找到可用的企业微信配置")
		return
	}

	// 创建企业微信服务
	wechatService := services.NewWechatService(config)

	// 同步部门数据
	if err := wechatService.SyncDepartments(); err != nil {
		logger.Errorf("同步部门数据失败: %v", err)
		utils.InternalServerError(c, "同步失败: "+err.Error())
		return
	}

	utils.Success(c, "同步成功")
}
