package models

import (
	"time"

	"gorm.io/gorm"
)

// WechatConfig 企业微信配置模型
type WechatConfig struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	CorpID      string         `json:"corp_id" gorm:"type:varchar(100);not null;comment:企业ID"`
	AgentID     int            `json:"agent_id" gorm:"type:int;not null;comment:应用ID"`
	Secret      string         `json:"secret" gorm:"type:varchar(200);not null;comment:应用Secret"`
	Name        string         `json:"name" gorm:"type:varchar(100);not null;comment:配置名称"`
	Description string         `json:"description" gorm:"type:text;comment:配置描述"`
	Status      int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1启用 0禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// WechatConfigListRequest 企业微信配置列表请求参数
type WechatConfigListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Name     string `form:"name"`
	Status   *int   `form:"status" binding:"omitempty,oneof=0 1"`
}

// WechatConfigCreateRequest 创建企业微信配置请求参数
type WechatConfigCreateRequest struct {
	CorpID      string `json:"corp_id" binding:"required,max=100"`
	AgentID     int    `json:"agent_id" binding:"required,min=1"`
	Secret      string `json:"secret" binding:"required,max=200"`
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Status      int    `json:"status" binding:"omitempty,oneof=0 1"`
}

// WechatConfigUpdateRequest 更新企业微信配置请求参数
type WechatConfigUpdateRequest struct {
	CorpID      string `json:"corp_id" binding:"required,max=100"`
	AgentID     int    `json:"agent_id" binding:"required,min=1"`
	Secret      string `json:"secret" binding:"required,max=200"`
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Status      int    `json:"status" binding:"omitempty,oneof=0 1"`
}

// WechatConfigResponse 企业微信配置响应数据
type WechatConfigResponse struct {
	ID          uint      `json:"id"`
	CorpID      string    `json:"corp_id"`
	AgentID     int       `json:"agent_id"`
	Secret      string    `json:"secret"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Status      int       `json:"status"`
	StatusText  string    `json:"status_text"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (w *WechatConfig) ToResponse() *WechatConfigResponse {
	statusText := "禁用"
	if w.Status == 1 {
		statusText = "启用"
	}

	return &WechatConfigResponse{
		ID:          w.ID,
		CorpID:      w.CorpID,
		AgentID:     w.AgentID,
		Secret:      w.Secret,
		Name:        w.Name,
		Description: w.Description,
		Status:      w.Status,
		StatusText:  statusText,
		CreatedAt:   w.CreatedAt,
		UpdatedAt:   w.UpdatedAt,
	}
}
