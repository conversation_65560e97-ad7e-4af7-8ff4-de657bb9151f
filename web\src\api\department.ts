import request from '@/utils/request'
import type { ApiResponse, PageInfo } from '@/types/common'

// 部门接口
export interface Department {
  id: number
  wechat_id: number
  name: string
  name_en: string
  parent_id: number
  sort: number
  status: number
  status_text: string
  children?: Department[]
  created_at: string
  updated_at: string
}

// 部门列表请求参数
export interface DepartmentListRequest {
  page?: number
  page_size?: number
  name?: string
  status?: number
  parent_id?: number
}

interface PageResponse<T = any> extends ApiResponse<T> {
  page: PageInfo
}

// 获取部门列表
export function getDepartmentList(params: DepartmentListRequest): Promise<PageResponse<Department[]>> {
  return request({
    url: '/admin/departments',
    method: 'get',
    params
  })
}

// 获取部门树
export function getDepartmentTree(): Promise<ApiResponse<Department[]>> {
  return request({
    url: '/admin/departments/tree',
    method: 'get'
  })
}

// 获取部门详情
export function getDepartment(id: number): Promise<ApiResponse<Department>> {
  return request({
    url: `/admin/departments/${id}`,
    method: 'get'
  })
}

// 同步部门数据
export function syncDepartments(): Promise<ApiResponse<any>> {
  return request({
    url: '/admin/departments/sync',
    method: 'post'
  })
}
