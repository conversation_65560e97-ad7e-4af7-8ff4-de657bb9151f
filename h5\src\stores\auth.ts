import {defineS<PERSON>} from "pinia"
import {ref} from "vue"
import {getToken, setToken, removeToken} from "@/utils/storage"
import{showFailToast, showDialog} from "vant"
import {login as useLogin, getUserInfo} from "@/api/user"
import {useUserStore, type UserInfo} from "./user"
export const useAuthStore = defineStore("userAuth", ()=>{
    let  token = ref(getToken())
    const setUserToken = (payload:string)=>{
          token.value = payload
          setToken(payload)
    }

    const clearToken = ()=>{
        token.value = null
        removeToken()
    }
    //用户登录
    const login = async ()=>{      
        if(!token.value){
            // 重新登陆
            const params = new URLSearchParams(window.location.search);
            const code = params.get("code");
            if(!code){
                showDialog({
                    message: '参数错误， 关闭页面重新从企业微信进入',
                }).then(() => {
                    window.location.href="/error"
                });
                return
            }
              
            const response = await useLogin(code)
            if(response.code!=200){
                showFailToast(response.msg)
               
                return
            }
            setUserToken(response.data)
        }
        const dataInfo = await getUserInfo()
        if(dataInfo.code!=200){
            showFailToast(dataInfo.msg)
            return
           
        }
        //更新用户信息
        const userInfo:UserInfo = {
            id:dataInfo.data.id,
            name:dataInfo.data.name,
            department:dataInfo.data.departments[0].name,
            avatar: dataInfo.data.avatar,
            phone: dataInfo.data.mobile,
            email: dataInfo.data.email

        }
        const userStore = useUserStore()
        userStore.setUserInfo(userInfo)
    }
    return{
        token,
        setUserToken,
        login,
        clearToken
    }
})