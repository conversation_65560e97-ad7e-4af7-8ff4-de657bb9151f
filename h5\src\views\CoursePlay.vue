<template>
  <div class="course-play-page">
    <!-- 头部导航 -->
    <van-nav-bar
      :title="courseDetail?.assessment_video?.title || '课程播放'"
      left-arrow
      @click-left="goBack"
      style="background-color: #1bb394"
    />

    <!-- 视频播放器 -->
    <div class="video-container">
      <video
        ref="videoPlayer"
        :src="currentVideo?.play_url"
        :poster="currentVideo?.cover_url"
        controls
        preload="metadata"
        @loadedmetadata="onVideoLoaded"
        @timeupdate="onTimeUpdate"
        @ended="onVideoEnded"
        class="video-player"
        controlsList="nodownload"
      >
        您的浏览器不支持视频播放
      </video>
    </div>

    <!-- 视频信息 -->
    <div class="video-info">
      <div class="video-title">{{ currentVideo?.title }}</div>
      <div class="video-duration">
        时长: {{ formatDuration(currentVideo?.duration || 0) }}
      </div>
    </div>

    <!-- 视频列表 -->
    <div class="video-list">
      <div class="list-header">
        <span class="list-title">课程列表</span>
        <span class="list-count">共 {{ videoList.length }} 课时</span>
      </div>

      <div class="list-content">
        <div
          v-for="(videoItem, index) in videoList"
          :key="videoItem.id"
          class="video-item"
          :class="{ active: currentVideoIndex === index }"
          @click="playVideo(index)"
        >
          <div class="video-item-left">
            <div class="video-index">{{ index + 1 }}</div>
            <div class="video-details">
              <div class="video-item-title">{{ videoItem.video?.title }}</div>
              <div class="video-item-duration">
                {{ formatDuration(videoItem.video?.duration || 0) }}
              </div>
            </div>
          </div>
          <div class="video-item-right">
            <van-icon
              v-if="currentVideoIndex === index"
              name="play-circle"
              color="#1bb394"
              size="20"
            />
            <van-icon v-else name="play-circle-o" color="#999" size="20" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getCourseDetail, updateVideoProgress } from "@/api/user";
import type {
  AssessmentRecord,
  AssessmentVideoRelation,
  Video,
} from "@/types/course";
import { showToast } from "vant";

const route = useRoute();
const router = useRouter();

// 响应式数据
const courseDetail = ref<AssessmentRecord | null>(null);
const videoList = ref<AssessmentVideoRelation[]>([]);
const currentVideoIndex = ref(0);
const videoPlayer = ref<HTMLVideoElement>();
const lastProgressUpdate = ref(0);

// 计算属性
const currentVideo = computed(() => {
  if (
    videoList.value.length > 0 &&
    currentVideoIndex.value < videoList.value.length
  ) {
    return videoList.value[currentVideoIndex.value].video;
  }
  return null;
});

// 格式化时长
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  }
  return `${minutes}:${secs.toString().padStart(2, "0")}`;
};

// 获取课程详情
const fetchCourseDetail = async () => {
  try {
    const id = Number(route.params.id);
    const response = await getCourseDetail(id);

    if (response.code === 200) {
      courseDetail.value = response.data;
      videoList.value = response.data.assessment_video.videos.sort(
        (a, b) => a.sort - b.sort
      );

      // 默认播放第一个视频
      if (videoList.value.length > 0) {
        currentVideoIndex.value = 0;
        await nextTick();
        loadCurrentVideo();
      }
    } else {
      showToast(response.msg || "获取课程详情失败");
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    showToast("获取课程详情失败");
  }
};

// 加载当前视频
const loadCurrentVideo = () => {
  if (videoPlayer.value && currentVideo.value) {
    videoPlayer.value.load();
  }
};

// 播放指定视频
const playVideo = async (index: number) => {
  if (index >= 0 && index < videoList.value.length) {
    currentVideoIndex.value = index;
    await nextTick();
    loadCurrentVideo();
  }
};

// 视频加载完成
const onVideoLoaded = () => {
  console.log("视频加载完成");
};

// 视频时间更新
const onTimeUpdate = () => {
  if (!videoPlayer.value || !currentVideo.value || !courseDetail.value) return;

  const currentTime = videoPlayer.value.currentTime;
  const duration = videoPlayer.value.duration;

  // 每5秒更新一次进度
  if (currentTime - lastProgressUpdate.value >= 5) {
    lastProgressUpdate.value = currentTime;
    updateProgress(currentTime, duration);
  }
};

// 视频播放结束
const onVideoEnded = () => {
  // 自动播放下一个视频
  if (currentVideoIndex.value < videoList.value.length - 1) {
    playVideo(currentVideoIndex.value + 1);
  } else {
    showToast("课程播放完成");
  }
};

// 更新观看进度
const updateProgress = async (currentTime: number, duration: number) => {
  if (!courseDetail.value || !currentVideo.value) return;

  try {
    const progress = Math.floor((currentTime / duration) * 100);
    await updateVideoProgress({
      assessment_record_id: courseDetail.value.id,
      video_id: currentVideo.value.id,
      progress,
      duration: Math.floor(currentTime),
    });
  } catch (error) {
    console.error("更新进度失败:", error);
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

onMounted(() => {
  fetchCourseDetail();
});
</script>

<style scoped>
.course-play-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

:deep(.van-nav-bar__title) {
  color: #fff;
}

:deep(.van-nav-bar .van-icon) {
  color: #fff;
}

.video-container {
  position: relative;
  width: 100%;
  background-color: #000;
}

.video-player {
  width: 100%;
  height: auto;
  max-height: 250px;
  display: block;
}

.video-info {
  background-color: #fff;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.video-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.video-duration {
  font-size: 12px;
  color: #999;
}

.video-list {
  background-color: #fff;
  margin-top: 8px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.list-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.list-count {
  font-size: 12px;
  color: #999;
}

.list-content {
  max-height: 400px;
  overflow-y: auto;
}

.video-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.video-item:hover {
  background-color: #f8f9fa;
}

.video-item.active {
  background-color: #f0f9ff;
  border-left: 3px solid #1bb394;
}

.video-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.video-index {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  margin-right: 12px;
}

.video-item.active .video-index {
  background-color: #1bb394;
  color: #fff;
}

.video-details {
  flex: 1;
}

.video-item-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.video-item-duration {
  font-size: 12px;
  color: #999;
}

.video-item-right {
  margin-left: 12px;
}
</style>
