<template>
  <div class="container">
    <van-nav-bar title="错误" fixed style="background-color: #1bb394" />
    <div class="tip">
      <van-icon name="warning" size="80" color="#1bb394" />
      <span>页面错误</span>
    </div>
  </div>
</template>

<script setup></script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.tip {
  margin-top: 70px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #1bb394;
  font-size: 20px;
}
:deep(.van-nav-bar__title) {
  color: #fff;
}
</style>
