/**
 * 教师详细信息
 */
export interface Teacher {
  id: number;
  name: string;
  avatar: string;
  mobile: string;
  email: string;
  position: string;
  status: number;
  sort: number;
  created_at: string;
  updated_at: string;
}

/**
 * 考核视频与教师的关联信息
 */
export interface AssessmentVideoTeacher {
  id: number;
  assessment_video_id: number;
  teacher_id: number;
  created_at: string;
  updated_at: string;
  teacher: Teacher;
}

/**
 * 考核分类信息
 */
export interface AssessmentCategory {
  id: number;
  name: string;
  description: string;
  sort: number;
  status: number;
  parent_id: number;
  created_at: string;
  updated_at: string;
}

/**
 * 视频文件信息
 */
export interface Video {
  id: number;
  title: string;
  description: string;
  file_id: string;
  play_url: string;
  cover_url: string;
  duration: number;
  size: number;
  category_id: number;
  status: number;
  created_at: string;
  updated_at: string;
}

/**
 * 考核主视频与具体视频文件的关联信息
 */
export interface AssessmentVideoRelation {
  id: number;
  assessment_video_id: number;
  video_id: number;
  sort: number;
  created_at: string;
  updated_at: string;
  video: Video;
}

/**
 * 考核视频信息 (包含新增的 videos 数组)
 */
export interface AssessmentVideo {
  id: number;
  title: string;
  description: string;
  category_id: number;
  exam_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  category: AssessmentCategory;
  videos: AssessmentVideoRelation[]; // 新增：具体的视频文件列表
  teachers: AssessmentVideoTeacher[];
}

/**
 * 考核记录 (主结构)
 */
export interface AssessmentRecord {
  id: number;
  employee_id: number;
  assessment_video_id: number;
  status: number;
  assigned_at: string;
  started_at: string | null;
  completed_at: string | null;
  score: number | null;
  video_progress: number;
  exam_score: number | null;
  exam_status: number;
  created_at: string;
  updated_at: string;
  assessment_video: AssessmentVideo;
}