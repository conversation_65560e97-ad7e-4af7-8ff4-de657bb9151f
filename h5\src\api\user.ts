import request from "@/utils/request"
import type { apiResponse, pageResponse} from "@/types/common"
import type {AssessmentRecord} from "@/types/course"

// 试卷响应接口
export interface ExamPaper {
  id: number;
  title: string;
  description: string;
  type: number;
  duration: number;
  total_score: number;
  pass_score: number;
  question_count: number;
  category_name: string;
  assessment_title: string; // 考核标题
  created_at: string;
  updated_at: string;
  assessment_record_id: number;
  exam_status: number;
  exam_score?: number;
  exam_start_time?: string;
}

// 试卷列表响应接口
export interface ExamListResponse {
  exam_papers: ExamPaper[];
  practice_papers: ExamPaper[];
}

export function login(code: string): Promise<apiResponse<string>> {
	return request.post("/login",{code})
}
export function getUserInfo():Promise<apiResponse>{
    return request.get("/getUserInfo")
}
export function getUserCourseList(params:any): Promise<pageResponse<AssessmentRecord[]>> {
    return request.get("/getCourseList", {params})
}

// 获取课程详情（包含视频列表）
export function getCourseDetail(id: number): Promise<apiResponse<AssessmentRecord>> {
    return request.get(`/getCourseDetail/${id}`)
}

// 获取用户可参与的试卷列表
export function getExamList(): Promise<apiResponse<ExamListResponse>> {
    return request.get("/getExamList")
}

// 更新视频观看进度
export function updateVideoProgress(data: {
    assessment_record_id: number;
    video_id: number;
    progress: number;
    duration: number;
}): Promise<apiResponse> { 
    // @ts-ignore
    return request.post("/updateVideoProgress", data, { showLoading: false })
}

// 考试相关接口

// 开始考试
export function startExam(data: {
    exam_id: number;
    assessment_record_id?: number;
}): Promise<apiResponse<{
    exam_record_id: number;
    status: string;
    message: string;
}>> {
    return request.post("/startExam", data)
}

// 获取考试详情（答题页面）
export function getExamDetail(examRecordId: number): Promise<apiResponse<ExamDetailResponse>> {
    return request.get(`/examDetail/${examRecordId}`)
}

// 保存单题答案
export function saveAnswer(data: {
    exam_record_id: number;
    question_id: number;
    answer: string;
}): Promise<apiResponse> {
    return request.post("/saveAnswer", data)
}

// 提交考试
export function submitExam(data: {
    exam_record_id: number;
    answers: Array<{
        question_id: number;
        answer: string;
    }>;
    submit_type: string;
}): Promise<apiResponse> {
    return request.post("/submitExam", data)
}

// 获取考试结果
export function getExamResult(examRecordId: number): Promise<apiResponse<ExamResultResponse>> {
    return request.get(`/examResult/${examRecordId}`)
}

// 获取学习统计
export function getLearningStats(): Promise<apiResponse<{
    video_time: number;
    practice_count: number;
    exam_count: number;
    avg_score: number;
}>> {
    return request.get("/getLearningStats")
}

// 获取考试记录
export function getExamRecords(): Promise<apiResponse<Array<{
    id: number;
    title: string;
    exam_time: string;
    score: number;
    duration: number;
    total_questions: number;
    correct_answers: number;
    is_passed: boolean;
}>>> {
    return request.get("/getExamRecords")
}

// 获取刷题记录
export function getPracticeRecords(): Promise<apiResponse<Array<{
    id: number;
    title: string;
    exam_time: string;
    score: number;
    duration: number;
    total_questions: number;
    correct_answers: number;
    is_passed: boolean;
}>>> {
    return request.get("/getPracticeRecords")
}

// 考试详情响应接口
export interface ExamDetailResponse {
    exam_record: ExamRecordResponse;
    questions: ExamQuestionDetail[];
    time_left: number;
}

// 考试记录响应接口
export interface ExamRecordResponse {
    id: number;
    employee_id: number;
    exam_id: number;
    assessment_record_id?: number;
    status: number;
    status_text: string;
    start_time?: string;
    end_time?: string;
    submit_time?: string;
    duration: number;
    score: number;
    total_score: number;
    pass_score: number;
    is_passed: boolean;
    correct_count: number;
    total_count: number;
    created_at: string;
    updated_at: string;
}

// 考试题目详情接口
export interface ExamQuestionDetail {
    id: number;
    question_id: number;
    score: number;
    sort: number;
    question: QuestionDetail;
    user_answer?: string;
    is_answered: boolean;
}

// 题目详情接口
export interface QuestionDetail {
    id: number;
    title: string;
    content: string;
    type: number;
    options: Array<{
        label: string;
        content: string;
    }>;
    correct_answer?: string;
    difficulty: number;
    category_id: number;
    status: number;
    created_at: string;
    updated_at: string;
}

// 考试结果响应接口
export interface ExamResultResponse {
    id: number;
    employee_id: number;
    exam_id: number;
    title:string;
    assessment_record_id?: number;
    status: number;
    status_text: string;
    start_time?: string;
    end_time?: string;
    submit_time?: string;
    duration: number;
    score: number;
    total_score: number;
    pass_score: number;
    is_passed: boolean;
    correct_count: number;
    total_count: number;
    answers: ExamAnswerDetail[];
    created_at: string;
    updated_at: string;
}

// 答题详情接口
export interface ExamAnswerDetail {
    id: number;
    exam_record_id: number;
    question_id: number;
    answer: string;
    is_correct: boolean;
    score: number;
    answer_time?: string;
    question?: QuestionDetail;
    created_at: string;
    updated_at: string;
}