package controllers

import (
	"math"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/services"
	"ai_select_admin/utils"
)

type AssessmentRecordController struct{}

// ListAssessmentRecords 获取考核记录列表
func (arc *AssessmentRecordController) ListAssessmentRecords(c *gin.Context) {
	var req models.AssessmentRecordListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.AssessmentRecord{}).
		Preload("Employee").
		Preload("AssessmentVideo").
		Preload("AssessmentVideo.Category").
		Preload("AssessmentVideo.Exam")

	// 按员工姓名搜索
	if req.EmployeeName != "" {
		query = query.Joins("JOIN hr_employee ON hr_employee.id = hr_assessment_record.employee_id").
			Where("hr_employee.name LIKE ?", "%"+req.EmployeeName+"%")
	}

	// 按考核视频标题搜索
	if req.AssessmentVideoTitle != "" {
		query = query.Joins("JOIN hr_assessment_videos ON hr_assessment_videos.id = hr_assessment_record.assessment_video_id").
			Where("hr_assessment_videos.title LIKE ?", "%"+req.AssessmentVideoTitle+"%")
	}

	// 按部门搜索
	if req.DepartmentID != nil {
		query = query.Joins("JOIN hr_employee ON hr_employee.id = hr_assessment_record.employee_id").
			Where("JSON_CONTAINS(hr_employee.department, ?)", strconv.Itoa(*req.DepartmentID))
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("hr_assessment_records.status = ?", *req.Status)
	}

	// 按员工ID筛选
	if req.EmployeeID != nil {
		query = query.Where("hr_assessment_records.employee_id = ?", *req.EmployeeID)
	}

	// 按考核视频ID筛选
	if req.AssessmentVideoID != nil {
		query = query.Where("hr_assessment_record.assessment_video_id = ?", *req.AssessmentVideoID)
	}

	// 按是否及格筛选
	if req.IsPassed != nil {
		switch *req.IsPassed {
		case 0: // 不及格
			query = query.Joins("JOIN hr_assessment_video ON hr_assessment_video.id = hr_assessment_record.assessment_video_id").
				Joins("JOIN hr_exam ON hr_exam.id = hr_assessment_video.exam_id").
				Where("hr_assessment_record.exam_score IS NOT NULL AND hr_assessment_record.exam_score < hr_exam.pass_score")
		case 1: // 及格
			query = query.Joins("JOIN hr_assessment_video ON hr_assessment_video.id = hr_assessment_record.assessment_video_id").
				Joins("JOIN hr_exam ON hr_exam.id = hr_assessment_video.exam_id").
				Where("hr_assessment_record.exam_score IS NOT NULL AND hr_assessment_record.exam_score >= hr_exam.pass_score")
		case 2: // 未考试
			query = query.Where("hr_assessment_record.exam_score IS NULL")
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取考核记录总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var assessmentRecords []models.AssessmentRecord
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("hr_assessment_record.id DESC").Offset(offset).Limit(req.PageSize).Find(&assessmentRecords).Error; err != nil {
		logger.Errorf("查询考核记录列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 更新考试状态和分数，并计算刷题数量
	for i := range assessmentRecords {
		ar := &assessmentRecords[i]
		// 如果有关联的考试试卷，查询考试记录
		if ar.AssessmentVideo != nil && ar.AssessmentVideo.Exam != nil && ar.AssessmentVideo.Exam.Type == 1 {
			var examRecord models.ExamRecord
			err := database.DB.Where("employee_id = ? AND assessment_record_id = ? AND status IN (2, 3)",
				ar.EmployeeID, ar.ID).
				Order("id DESC").
				First(&examRecord).Error

			if err == nil {
				// 找到考试记录，更新分数
				ar.ExamScore = &examRecord.Score
				// 更新数据库 - 只更新考核记录表，避免影响关联的员工数据
				database.DB.Model(&models.AssessmentRecord{}).
					Where("id = ?", ar.ID).
					Updates(map[string]interface{}{
						"exam_score": examRecord.Score,
					})
			}
		}
	}

	// 转换为响应格式并计算刷题数量
	var responses []*models.AssessmentRecordResponse
	for _, ar := range assessmentRecords {
		resp := ar.ToResponse()

		// 计算该员工的刷题数量（已完成的刷题试卷对应的题目数量总和）
		var practiceCount int64
		database.DB.Table("hr_exam_record").
			Select("COALESCE(SUM(hr_exam_record.total_count), 0)").
			Joins("JOIN hr_exam ON hr_exam.id = hr_exam_record.exam_id").
			Where("hr_exam_record.employee_id = ? AND hr_exam.type = 2 AND hr_exam_record.status = 2", ar.EmployeeID).
			Scan(&practiceCount)

		resp.PracticeCount = int(practiceCount)
		responses = append(responses, resp)
	}

	// 计算分页信息
	pages := int(math.Ceil(float64(total) / float64(req.PageSize)))
	pageInfo := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Total:   total,
		Pages:   pages,
	}

	utils.PageSuccess(c, responses, pageInfo)
}

// GetAssessmentRecord 获取考核记录详情
func (arc *AssessmentRecordController) GetAssessmentRecord(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考核记录ID")
		return
	}

	var assessmentRecord models.AssessmentRecord
	if err := database.DB.Preload("Employee").
		Preload("AssessmentVideo").
		Preload("AssessmentVideo.Category").
		Preload("AssessmentVideo.Exam").
		First(&assessmentRecord, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "考核记录不存在")
		} else {
			logger.Errorf("查询考核记录失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, assessmentRecord.ToResponse())
}

// CreateAssessmentRecord 创建考核记录
func (arc *AssessmentRecordController) CreateAssessmentRecord(c *gin.Context) {
	var req models.AssessmentRecordCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证考核视频是否存在
	var assessmentVideo models.AssessmentVideo
	if err := database.DB.First(&assessmentVideo, req.AssessmentVideoID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.BadRequest(c, "考核视频不存在")
		} else {
			logger.Errorf("查询考核视频失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 验证员工是否存在
	var employeeCount int64
	if err := database.DB.Model(&models.Employee{}).Where("id IN ?", req.EmployeeIDs).Count(&employeeCount).Error; err != nil {
		logger.Errorf("验证员工失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if int(employeeCount) != len(req.EmployeeIDs) {
		utils.BadRequest(c, "存在无效的员工")
		return
	}

	// 检查是否已存在相同的考核记录
	var existingCount int64
	if err := database.DB.Model(&models.AssessmentRecord{}).
		Where("employee_id IN ? AND assessment_video_id = ?", req.EmployeeIDs, req.AssessmentVideoID).
		Count(&existingCount).Error; err != nil {
		logger.Errorf("检查重复考核记录失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if existingCount > 0 {
		utils.BadRequest(c, "部分员工已存在该考核记录")
		return
	}

	// 开始事务
	tx := database.DB.Begin()

	// 解析考试开始时间
	var examStartTime *time.Time
	if req.ExamStartTime != nil && *req.ExamStartTime != "" {
		parsedTime, err := time.Parse("2006-01-02 15:04:05", *req.ExamStartTime)
		if err != nil {
			utils.BadRequest(c, "考试开始时间格式错误，请使用 YYYY-MM-DD HH:mm:ss 格式")
			return
		}
		examStartTime = &parsedTime
	}

	// 批量创建考核记录
	var assessmentRecords []models.AssessmentRecord
	now := time.Now()
	for _, employeeID := range req.EmployeeIDs {
		record := models.AssessmentRecord{
			EmployeeID:        employeeID,
			AssessmentVideoID: req.AssessmentVideoID,
			Status:            models.AssessmentRecordStatusNotStarted,
			AssignedAt:        now,
			ExamStartTime:     examStartTime,
		}
		assessmentRecords = append(assessmentRecords, record)
	}

	if err := tx.Create(&assessmentRecords).Error; err != nil {
		tx.Rollback()
		logger.Errorf("创建考核记录失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	tx.Commit()

	logger.Infof("批量创建考核记录成功: %d条", len(assessmentRecords))

	// 发送企业微信通知
	go func() {
		// 获取员工的企业微信用户ID
		var employees []models.Employee
		if err := database.DB.Where("id IN ?", req.EmployeeIDs).Find(&employees).Error; err != nil {
			logger.Errorf("获取员工信息失败: %v", err)
			return
		}

		var userIDs []string
		for _, emp := range employees {
			if emp.UserID != "" {
				userIDs = append(userIDs, emp.UserID)
			}
		}

		if len(userIDs) > 0 {
			// 获取企业微信配置
			config, err := services.GetDefaultWechatConfig()
			if err != nil {
				logger.Errorf("获取企业微信配置失败: %v", err)
				return
			}

			// 创建企业微信服务
			wechatService := services.NewWechatService(config)

			// 发送考核通知
			if err := wechatService.SendAssessmentNotification(userIDs, assessmentVideo.Title); err != nil {
				logger.Errorf("发送考核通知失败: %v", err)
			} else {
				logger.Infof("考核通知发送成功，通知用户数: %d", len(userIDs))
			}
		}
	}()

	utils.SuccessWithMsg(c, "创建成功", map[string]interface{}{
		"count": len(assessmentRecords),
	})
}

// UpdateAssessmentRecord 更新考核记录
func (arc *AssessmentRecordController) UpdateAssessmentRecord(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考核记录ID")
		return
	}

	var req models.AssessmentRecordUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查找考核记录
	var assessmentRecord models.AssessmentRecord
	if err := database.DB.First(&assessmentRecord, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "考核记录不存在")
		} else {
			logger.Errorf("查询考核记录失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 更新字段
	updates := make(map[string]interface{})

	if req.Status != nil {
		updates["status"] = *req.Status
		// 根据状态更新时间
		now := time.Now()
		switch *req.Status {
		case models.AssessmentRecordStatusInProgress:
			if assessmentRecord.StartedAt == nil {
				updates["started_at"] = now
			}
		case models.AssessmentRecordStatusCompleted:
			if assessmentRecord.CompletedAt == nil {
				updates["completed_at"] = now
			}
		}
	}

	if req.VideoProgress != nil {
		updates["video_progress"] = *req.VideoProgress
	}

	if req.ExamScore != nil {
		updates["exam_score"] = *req.ExamScore
	}

	if req.ExamStartTime != nil {
		// 解析时间字符串
		if *req.ExamStartTime != "" {
			examStartTime, err := time.Parse("2006-01-02 15:04:05", *req.ExamStartTime)
			if err != nil {
				utils.BadRequest(c, "考试开始时间格式错误，请使用 YYYY-MM-DD HH:mm:ss 格式")
				return
			}
			updates["exam_start_time"] = examStartTime
		} else {
			updates["exam_start_time"] = nil
		}
	}

	// 执行更新
	if err := database.DB.Model(&assessmentRecord).Updates(updates).Error; err != nil {
		logger.Errorf("更新考核记录失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 重新查询以获取关联数据
	if err := database.DB.Preload("Employee").
		Preload("AssessmentVideo").
		Preload("AssessmentVideo.Category").
		Preload("AssessmentVideo.Exam").
		First(&assessmentRecord, uint(id)).Error; err != nil {
		logger.Errorf("查询更新后的考核记录失败: %v", err)
	}

	logger.Infof("更新考核记录成功: %d", id)
	utils.SuccessWithMsg(c, "更新成功", assessmentRecord.ToResponse())
}

// DeleteAssessmentRecord 删除考核记录
func (arc *AssessmentRecordController) DeleteAssessmentRecord(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考核记录ID")
		return
	}

	// 查找考核记录
	var assessmentRecord models.AssessmentRecord
	if err := database.DB.First(&assessmentRecord, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "考核记录不存在")
		} else {
			logger.Errorf("查询考核记录失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 软删除考核记录
	if err := database.DB.Delete(&assessmentRecord).Error; err != nil {
		logger.Errorf("删除考核记录失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("删除考核记录成功: %s", assessmentRecord.ID)
	utils.SuccessWithMsg(c, "删除成功", nil)
}

// GetExamDetail 获取考试详情
func (arc *AssessmentRecordController) GetExamDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考核记录ID")
		return
	}

	// 查找考核记录
	var assessmentRecord models.AssessmentRecord
	if err := database.DB.Preload("Employee").
		Preload("AssessmentVideo").
		Preload("AssessmentVideo.Exam").
		First(&assessmentRecord, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "考核记录不存在")
		} else {
			logger.Errorf("查询考核记录失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	logger.Infof("记录的数据 %+v\n", assessmentRecord)

	// 检查是否为考试试卷类型
	if assessmentRecord.AssessmentVideo == nil {
		utils.BadRequest(c, "考核记录未关联考核视频")
		return
	}
	if assessmentRecord.AssessmentVideo.Exam == nil {
		utils.BadRequest(c, "该考核视频未关联试卷")
		return
	}
	if assessmentRecord.AssessmentVideo.Exam.Type != 1 {
		utils.BadRequest(c, "该考核关联的是刷题试卷，不是考试试卷，无法查看考试详情")
		return
	}

	// 查找考试记录（只查找与考核记录关联的考试试卷的考试记录）
	var examRecord models.ExamRecord
	err = database.DB.Preload("Answers.Question", func(db *gorm.DB) *gorm.DB {
		return db.Select("id, title, content, type, options, correct_answer, difficulty, category_id, status, created_at, updated_at")
	}).
		Where("employee_id = ? AND assessment_record_id = ? AND exam_id = ? AND status IN (2, 3)",
			assessmentRecord.EmployeeID, assessmentRecord.ID, assessmentRecord.AssessmentVideo.Exam.ID).
		Order("id DESC").
		First(&examRecord).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "考试记录不存在")
		} else {
			logger.Errorf("查询考试记录失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 构建答题详情，包含题目选项
	var answerDetails []gin.H
	for _, answer := range examRecord.Answers {
		if answer.Question != nil {
			// 解析题目选项
			answer.Question.ParseOptions()
			answerDetail := gin.H{
				"id":          answer.ID,
				"question_id": answer.QuestionID,
				"answer":      answer.Answer,
				"is_correct":  answer.IsCorrect,
				"score":       answer.Score,
				"answer_time": answer.AnswerTime,
				"question":    answer.Question.ToResponse(),
			}
			answerDetails = append(answerDetails, answerDetail)
		}
	}

	// 构建考试详情响应
	examDetail := gin.H{
		"score":         examRecord.Score,
		"duration":      examRecord.Duration,
		"correct_count": examRecord.CorrectCount,
		"total_count":   examRecord.TotalCount,
		"is_passed":     examRecord.IsPassed,
		"start_time":    examRecord.StartTime,
		"end_time":      examRecord.EndTime,
		"submit_time":   examRecord.SubmitTime,
		"answers":       answerDetails,
	}

	utils.Success(c, examDetail)
}

// FixAssessmentRecordStatus 修复考核记录状态（临时接口）
func (arc *AssessmentRecordController) FixAssessmentRecordStatus(c *gin.Context) {
	// 查找所有有考试得分但状态不是已完成的考核记录
	var assessmentRecords []models.AssessmentRecord
	err := database.DB.Where("exam_score IS NOT NULL AND status != ?", models.AssessmentRecordStatusCompleted).
		Find(&assessmentRecords).Error

	if err != nil {
		logger.Errorf("查询需要修复的考核记录失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 批量更新状态
	updatedCount := 0
	for _, record := range assessmentRecords {
		err := database.DB.Model(&record).Updates(map[string]interface{}{
			"status":       models.AssessmentRecordStatusCompleted,
			"completed_at": time.Now(),
		}).Error

		if err != nil {
			logger.Errorf("更新考核记录状态失败: %v", err)
		} else {
			updatedCount++
		}
	}

	utils.SuccessWithMsg(c, "状态修复完成", gin.H{
		"total_records": len(assessmentRecords),
		"updated_count": updatedCount,
	})
}
