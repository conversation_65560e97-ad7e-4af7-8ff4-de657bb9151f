import { defineStore } from 'pinia'
import { ref } from 'vue'
import { mockCourses, mockCourseCategories } from '@/utils/mockData'

export interface Course {
  id: number
  title: string
  description: string
  cover_image: string
  category_id: number
  category_name: string
  teacher_names: string[]
  teacher_avatars: string[]
  created_at: string
  updated_at: string
}

export interface CourseCategory {
  id: number
  name: string
  parent_id: number
  sort: number
  created_at: string
  updated_at: string
}

export const useCourseStore = defineStore('course', () => {
  const courses = ref<Course[]>([])
  const categories = ref<CourseCategory[]>([])
  const loading = ref(false)

  // 获取课程列表
  const fetchCourses = async () => {
    loading.value = true
    try {
      // 使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟
      courses.value = mockCourses
    } catch (error) {
      console.error('获取课程列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      // 使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 200)) // 模拟网络延迟
      categories.value = mockCourseCategories
    } catch (error) {
      console.error('获取分类列表失败:', error)
    }
  }

  return {
    courses,
    categories,
    loading,
    fetchCourses,
    fetchCategories
  }
})
