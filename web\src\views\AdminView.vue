<template>
  <div class="admin-management">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、邮箱或昵称"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
        <el-col :span="8" class="text-right">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            添加管理员
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar
              :size="40"
              :src="getAvatarUrl(row.avatar)"
              :icon="UserFilled"
            />
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="nickname" label="昵称" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "正常" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录" width="180">
          <template #default="{ row }">
            {{ formatDate(row.last_login) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :disabled="row.id === currentUserId"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
        </el-form-item>

        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <!-- 编辑时的密码修改选项 -->
        <template v-if="isEdit">
          <el-form-item>
            <el-checkbox v-model="showPasswordEdit">修改密码</el-checkbox>
          </el-form-item>

          <el-form-item label="新密码" prop="password" v-if="showPasswordEdit">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入新密码（至少6位）"
              show-password
            />
          </el-form-item>

          <el-form-item
            label="确认密码"
            prop="confirmPassword"
            v-if="showPasswordEdit"
          >
            <el-input
              v-model="form.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
            />
          </el-form-item>
        </template>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称" />
        </el-form-item>

        <el-form-item label="头像" prop="avatar">
          <div class="avatar-upload-container">
            <el-upload
              class="avatar-uploader"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleAvatarChange"
              accept="image/*"
            >
              <img
                v-if="form.avatar"
                :src="getImageUrl(form.avatar)"
                class="avatar"
              />
              <div v-else class="avatar-uploader-placeholder">
                <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
                <div class="upload-text">点击上传头像</div>
              </div>
            </el-upload>
            <div class="upload-tips">
              <p>• 支持 JPG、PNG、GIF、BMP 格式</p>
              <p>• 文件大小不超过 5MB</p>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules,
} from "element-plus";
import { Search, Plus, UserFilled } from "@element-plus/icons-vue";
import { useAuthStore } from "@/stores/auth";
import { getImageUrl, getAvatarUrl } from "@/utils/image";
import { uploadToCOS, validateFile } from "@/utils/cos";
import {
  getAdminList,
  createAdmin,
  updateAdmin,
  deleteAdmin,
} from "@/api/admin";
import type {
  Admin,
  CreateAdminRequest,
  UpdateAdminRequest,
} from "@/types/admin";

const authStore = useAuthStore();

const loading = ref(false);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const isEdit = ref(false);
const showPasswordEdit = ref(false);
const formRef = ref();

const tableData = ref<Admin[]>([]);
const currentUserId = computed(() => authStore.userInfo?.id);

const searchForm = reactive({
  keyword: "",
});

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

const form = reactive({
  id: 0,
  username: "",
  password: "",
  confirmPassword: "",
  email: "",
  nickname: "",
  avatar: "",
  status: 1,
});

const dialogTitle = computed(() =>
  isEdit.value ? "编辑管理员" : "添加管理员"
);

const formRules = computed(() => {
  const rules: any = {
    username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
    email: [
      { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" },
    ],
  };

  // 添加时密码必填
  if (!isEdit.value) {
    rules.password = [
      { required: true, message: "请输入密码", trigger: "blur" },
      { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
    ];
  } else if (showPasswordEdit.value) {
    // 编辑时如果选择修改密码，则密码必填
    rules.password = [
      { required: true, message: "请输入新密码", trigger: "blur" },
      { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
    ];
    rules.confirmPassword = [
      { required: true, message: "请确认密码", trigger: "blur" },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value !== form.password) {
            callback(new Error("两次输入的密码不一致"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ];
  }

  return rules;
});

const formatDate = (dateString: string | null) => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleString("zh-CN");
};

// 头像上传处理方法
const handleAvatarChange = async (uploadFile: any) => {
  const file = uploadFile.raw;
  if (!file) return;

  try {
    // 验证文件
    validateFile(file, {
      maxSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/bmp"],
    });

    // 显示上传中状态
    ElMessage.info("正在上传头像...");

    // 上传到COS
    const cosUrl = await uploadToCOS(file);

    // 直接使用完整的HTTPS URL
    form.avatar = cosUrl;
    ElMessage.success("头像上传成功");
  } catch (error: any) {
    console.error("头像上传失败:", error);
    ElMessage.error(error.message || "头像上传失败");
  }
};

const fetchData = async () => {
  loading.value = true;
  try {
    const response = await getAdminList({
      page: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword,
    });

    tableData.value = response.data || [];
    pagination.total = response.page.total;
  } catch (error) {
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

const resetSearch = () => {
  searchForm.keyword = "";
  pagination.current = 1;
  fetchData();
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchData();
};

const handleCurrentChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handleSortChange = () => {
  fetchData();
};

const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
};

const handleEdit = (row: Admin) => {
  isEdit.value = true;
  form.id = row.id;
  form.username = row.username;
  form.password = "";
  form.confirmPassword = "";
  form.email = row.email;
  form.nickname = row.nickname;
  form.avatar = row.avatar;
  form.status = row.status;
  showPasswordEdit.value = false;
  dialogVisible.value = true;
};

const handleDelete = async (row: Admin) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除管理员 "${row.username}" 吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await deleteAdmin(row.id);
    ElMessage.success("删除成功");
    fetchData();
  } catch (error: any) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true;
      try {
        if (isEdit.value) {
          const data: UpdateAdminRequest = {
            email: form.email,
            nickname: form.nickname,
            avatar: form.avatar,
            status: form.status,
          };
          // 如果选择修改密码，则包含密码字段
          if (showPasswordEdit.value && form.password) {
            data.password = form.password;
          }
          await updateAdmin(form.id, data);
          ElMessage.success("更新成功");
        } else {
          const data: CreateAdminRequest = {
            username: form.username,
            password: form.password,
            email: form.email,
            nickname: form.nickname,
            avatar: form.avatar,
            status: form.status,
          };
          await createAdmin(data);
          ElMessage.success("创建成功");
        }

        dialogVisible.value = false;
        fetchData();
      } catch (error) {
        ElMessage.error(isEdit.value ? "更新失败" : "创建失败");
      } finally {
        submitLoading.value = false;
      }
    }
  });
};

const resetForm = () => {
  form.id = 0;
  form.username = "";
  form.password = "";
  form.confirmPassword = "";
  form.email = "";
  form.nickname = "";
  form.avatar = "";
  form.status = 1;
  showPasswordEdit.value = false;
  formRef.value?.resetFields();
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.admin-management {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.text-right {
  text-align: right;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 头像上传容器 */
.avatar-upload-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.avatar-uploader .el-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  background: #fafafa;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
  border-radius: 6px;
  object-fit: cover;
}

.avatar-uploader-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.avatar-uploader-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  color: #8c939d;
}

.upload-tips {
  flex: 1;
  padding-left: 20px;
}

.upload-tips p {
  margin: 0 0 5px 0;
  font-size: 12px;
  color: #999;
}
</style>
