package services

import (
	"ai_select_admin/logger"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// SSEClient SSE客户端连接
type SSEClient struct {
	ID       string
	Channel  chan string
	Context  *gin.Context
	IsClosed bool
}

// SSEService SSE服务
type SSEService struct {
	clients map[string]*SSEClient
	mutex   sync.RWMutex
}

// VideoStatusUpdateMessage 视频状态更新消息
type VideoStatusUpdateMessage struct {
	Type       string `json:"type"`
	VideoID    uint   `json:"video_id"`
	Status     int    `json:"status"`
	StatusText string `json:"status_text"`
	PlayURL    string `json:"play_url,omitempty"`
	CoverURL   string `json:"cover_url,omitempty"`
	Duration   int    `json:"duration,omitempty"`
	Width      int    `json:"width,omitempty"`
	Height     int    `json:"height,omitempty"`
	FileSize   int64  `json:"file_size,omitempty"`
}

var sseService *SSEService
var once sync.Once

// GetSSEService 获取SSE服务实例（单例模式）
func GetSSEService() *SSEService {
	once.Do(func() {
		sseService = &SSEService{
			clients: make(map[string]*SSEClient),
		}
	})
	return sseService
}

// AddClient 添加客户端连接
func (s *SSEService) AddClient(clientID string, c *gin.Context) *SSEClient {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 如果客户端已存在，先关闭旧连接
	if existingClient, exists := s.clients[clientID]; exists {
		existingClient.IsClosed = true
		close(existingClient.Channel)
		delete(s.clients, clientID)
	}

	client := &SSEClient{
		ID:       clientID,
		Channel:  make(chan string, 10), // 缓冲通道
		Context:  c,
		IsClosed: false,
	}

	s.clients[clientID] = client
	logger.Infof("SSE客户端连接: %s, 当前连接数: %d", clientID, len(s.clients))

	return client
}

// RemoveClient 移除客户端连接
func (s *SSEService) RemoveClient(clientID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if client, exists := s.clients[clientID]; exists {
		client.IsClosed = true
		close(client.Channel)
		delete(s.clients, clientID)
		logger.Infof("SSE客户端断开: %s, 当前连接数: %d", clientID, len(s.clients))
	}
}

// BroadcastVideoStatusUpdate 广播视频状态更新
func (s *SSEService) BroadcastVideoStatusUpdate(videoID uint, status int, statusText string, playURL, coverURL string, duration, width, height int, fileSize int64) {
	message := VideoStatusUpdateMessage{
		Type:       "video_status_update",
		VideoID:    videoID,
		Status:     status,
		StatusText: statusText,
		PlayURL:    playURL,
		CoverURL:   coverURL,
		Duration:   duration,
		Width:      width,
		Height:     height,
		FileSize:   fileSize,
	}

	messageJSON, err := json.Marshal(message)
	if err != nil {
		logger.Errorf("序列化SSE消息失败: %v", err)
		return
	}

	messageStr := fmt.Sprintf("data: %s\n\n", string(messageJSON))

	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 广播给所有连接的客户端
	for clientID, client := range s.clients {
		if !client.IsClosed {
			select {
			case client.Channel <- messageStr:
				logger.Infof("发送SSE消息给客户端 %s: 视频ID=%d, 状态=%d", clientID, videoID, status)
			default:
				// 通道已满，移除客户端
				logger.Warnf("客户端 %s 通道已满，移除连接", clientID)
				client.IsClosed = true
				close(client.Channel)
				delete(s.clients, clientID)
			}
		}
	}
}

// SendHeartbeat 发送心跳消息
func (s *SSEService) SendHeartbeat() {
	heartbeatMessage := fmt.Sprintf("data: {\"type\": \"heartbeat\", \"timestamp\": %d}\n\n",
		getCurrentTimestamp())

	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for clientID, client := range s.clients {
		if !client.IsClosed {
			select {
			case client.Channel <- heartbeatMessage:
				// 心跳发送成功
			default:
				// 通道已满，移除客户端
				logger.Warnf("客户端 %s 心跳发送失败，移除连接", clientID)
				client.IsClosed = true
				close(client.Channel)
				delete(s.clients, clientID)
			}
		}
	}
}

// GetClientCount 获取当前连接的客户端数量
func (s *SSEService) GetClientCount() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return len(s.clients)
}

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return time.Now().Unix()
}
