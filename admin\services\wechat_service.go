package services

import (
	"ai_select_admin/config"
	"ai_select_admin/database"
	"ai_select_admin/internal/redis"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"gorm.io/gorm"
)

// WechatConfig 企业微信配置
type WechatConfig struct {
	CorpID  string
	Secret  string
	AgentID int
}

// WechatService 企业微信服务
type WechatService struct {
	config *WechatConfig
}

// NewWechatService 创建企业微信服务实例
func NewWechatService(config *WechatConfig) *WechatService {
	return &WechatService{
		config: config,
	}
}

// GetDefaultWechatConfig 获取默认配置
func GetDefaultWechatConfig() (*WechatConfig, error) {
	// 优先从数据库获取启用的企业微信配置
	var wechatConfig models.WechatConfig
	if err := database.DB.Where("status = ?", 1).First(&wechatConfig).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果数据库中没有配置，则使用配置文件中的默认配置
			if config.AppConfig == nil {
				return nil, fmt.Errorf("配置未初始化")
			}
			return &WechatConfig{
				CorpID:  config.AppConfig.CopConfig.CorpId,
				Secret:  config.AppConfig.CopConfig.CorpSecret,
				AgentID: config.AppConfig.CopConfig.AgentId,
			}, nil
		}
		return nil, err
	}

	return &WechatConfig{
		CorpID:  wechatConfig.CorpID,
		Secret:  wechatConfig.Secret,
		AgentID: wechatConfig.AgentID,
	}, nil
}

// AccessTokenResponse 获取access_token响应
type AccessTokenResponse struct {
	ErrCode     int    `json:"errcode"`
	ErrMsg      string `json:"errmsg"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

// GetAccessToken 获取access_token
func (w *WechatService) GetAccessToken() (string, error) {

	accessToken, _ := redis.Rdb.Get(redis.Ctx, "wechat_access_token").Result()
	if accessToken != "" {
		return accessToken, nil
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s",
		w.config.CorpID, w.config.Secret)

	resp, err := http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var tokenResp AccessTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return "", err
	}

	if tokenResp.ErrCode != 0 {
		return "", fmt.Errorf("获取access_token失败: %s", tokenResp.ErrMsg)
	}
	redis.Rdb.Set(redis.Ctx, "wechat_access_token", tokenResp.AccessToken, time.Duration(tokenResp.ExpiresIn)*time.Second).Err()
	return tokenResp.AccessToken, nil
}

// DepartmentListResponse 部门列表响应
type DepartmentListResponse struct {
	ErrCode    int                    `json:"errcode"`
	ErrMsg     string                 `json:"errmsg"`
	Department []WechatDepartmentInfo `json:"department"`
}

// WechatDepartmentInfo 企业微信部门信息
type WechatDepartmentInfo struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	NameEn   string `json:"name_en"`
	ParentID int    `json:"parentid"`
	Order    int    `json:"order"`
}

// GetDepartmentList 获取部门列表
func (w *WechatService) GetDepartmentList(accessToken string) ([]WechatDepartmentInfo, error) {
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token=%s", accessToken)

	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	logger.Infof("获取部门列表的响应: %s", string(body))
	if err != nil {
		return nil, err
	}

	var deptResp DepartmentListResponse
	if err := json.Unmarshal(body, &deptResp); err != nil {
		return nil, err
	}

	if deptResp.ErrCode != 0 {
		return nil, fmt.Errorf("获取部门列表失败: %s", deptResp.ErrMsg)
	}

	return deptResp.Department, nil
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	ErrCode  int              `json:"errcode"`
	ErrMsg   string           `json:"errmsg"`
	UserList []WechatUserInfo `json:"userlist"`
}

// WechatUserInfo 企业微信用户信息
type WechatUserInfo struct {
	UserID         string `json:"userid"`
	Name           string `json:"name"`
	Department     []int  `json:"department"`
	Order          []int  `json:"order"`
	Position       string `json:"position"`
	Mobile         string `json:"mobile"`
	Gender         string `json:"gender"`
	Email          string `json:"email"`
	Avatar         string `json:"avatar"`
	Status         int    `json:"status"`
	IsLeaderInDept []int  `json:"is_leader_in_dept"`
	Telephone      string `json:"telephone"`
	Alias          string `json:"alias"`
	Address        string `json:"address"`
	OpenUserID     string `json:"open_userid"`
	MainDepartment int    `json:"main_department"`
}

// GetDepartmentUsers 获取部门成员
func (w *WechatService) GetDepartmentUsers(accessToken string, departmentID int, fetchChild bool) ([]WechatUserInfo, error) {
	fetchChildParam := 0
	if fetchChild {
		fetchChildParam = 1
	}

	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=%s&department_id=%d&fetch_child=%d",
		accessToken, departmentID, fetchChildParam)

	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	logger.Infof("获取部门成员的响应: %s", string(body))
	if err != nil {
		return nil, err
	}

	var userResp UserListResponse
	if err := json.Unmarshal(body, &userResp); err != nil {
		return nil, err
	}

	if userResp.ErrCode != 0 {
		return nil, fmt.Errorf("获取部门成员失败: %s", userResp.ErrMsg)
	}

	return userResp.UserList, nil
}

// SyncDepartments 同步部门数据
func (w *WechatService) SyncDepartments() error {
	accessToken, err := w.GetAccessToken()
	if err != nil {
		return err
	}
	logger.Infof("获取到的access_token: %s", accessToken)
	departments, err := w.GetDepartmentList(accessToken)
	if err != nil {
		return err
	}

	// 开始事务
	tx := database.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 确保事务在函数结束时被处理
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// 现在没有外键约束，可以直接处理所有部门
	for _, dept := range departments {
		var existingDept models.Department
		err := tx.Where("wechat_id = ?", dept.ID).First(&existingDept).Error

		if err != nil && err != gorm.ErrRecordNotFound {
			tx.Rollback()
			return err
		}

		if err == gorm.ErrRecordNotFound {
			// 创建新部门
			newDept := models.Department{
				WechatID: dept.ID,
				Name:     dept.Name,
				NameEn:   dept.NameEn,
				ParentID: dept.ParentID,
				Sort:     dept.Order,
				Status:   1,
			}
			if err := tx.Create(&newDept).Error; err != nil {
				tx.Rollback()
				return err
			}
		} else {
			// 更新现有部门
			existingDept.Name = dept.Name
			existingDept.NameEn = dept.NameEn
			existingDept.ParentID = dept.ParentID
			existingDept.Sort = dept.Order
			if err := tx.Save(&existingDept).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	return tx.Commit().Error
}

// SyncEmployees 同步员工数据
func (w *WechatService) SyncEmployees() error {
	accessToken, err := w.GetAccessToken()
	if err != nil {
		return err
	}

	// 获取所有部门
	departments, err := w.GetDepartmentList(accessToken)
	if err != nil {
		return err
	}

	// 开始事务
	tx := database.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 确保事务在函数结束时被处理
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// 用于去重的用户ID集合和存储从企业微信获取到的所有用户ID
	processedUsers := make(map[string]bool)
	wechatUserIDs := make(map[string]bool)

	// 收集所有企业微信用户数据
	for _, dept := range departments {
		users, err := w.GetDepartmentUsers(accessToken, dept.ID, false)
		logger.Infof("获取部门成员的响应: %v", users)
		if err != nil {
			continue // 跳过错误的部门，继续处理其他部门
		}

		for _, user := range users {
			// 避免重复处理同一用户
			if processedUsers[user.UserID] {
				continue
			}
			processedUsers[user.UserID] = true
			wechatUserIDs[user.UserID] = true

			var existingEmployee models.Employee
			err := tx.Where("user_id = ?", user.UserID).First(&existingEmployee).Error

			if err != nil && err != gorm.ErrRecordNotFound {
				tx.Rollback()
				return err
			}

			if err == gorm.ErrRecordNotFound {
				// 3. 如果获取到的数据在表中没有对应的记录，添加该记录到表中
				newEmployee := models.Employee{
					UserID:         user.UserID,
					Name:           user.Name,
					Position:       user.Position,
					Mobile:         user.Mobile,
					Gender:         user.Gender,
					Email:          user.Email,
					Avatar:         user.Avatar,
					Telephone:      user.Telephone,
					Alias:          user.Alias,
					Address:        user.Address,
					OpenUserID:     user.OpenUserID,
					MainDeptID:     user.MainDepartment,
					Status:         models.EmployeeStatus(user.Status),
					IsLeaderData:   user.IsLeaderInDept,
					DepartmentData: user.Department,
					OrderData:      user.Order,
				}
				if err := tx.Create(&newEmployee).Error; err != nil {
					tx.Rollback()
					return err
				}

				// 建立员工和部门的关联关系
				if err := w.syncEmployeeDepartments(tx, &newEmployee, user.Department); err != nil {
					tx.Rollback()
					return err
				}
				logger.Infof("新增员工: %s (%s)", user.Name, user.UserID)
			} else {
				// 1. 如果表中存在该员工，更新员工信息
				// 注意：Gender、Avatar、Mobile、Email、Address 字段保持原样不更新
				existingEmployee.Name = user.Name
				existingEmployee.Position = user.Position
				// existingEmployee.Mobile = user.Mobile // 保持原样
				// existingEmployee.Gender = user.Gender // 保持原样
				// existingEmployee.Email = user.Email // 保持原样
				// existingEmployee.Avatar = user.Avatar // 保持原样
				existingEmployee.Telephone = user.Telephone
				existingEmployee.Alias = user.Alias
				// existingEmployee.Address = user.Address // 保持原样
				existingEmployee.OpenUserID = user.OpenUserID
				existingEmployee.MainDeptID = user.MainDepartment
				existingEmployee.Status = models.EmployeeStatus(user.Status)
				existingEmployee.IsLeaderData = user.IsLeaderInDept
				existingEmployee.DepartmentData = user.Department
				existingEmployee.OrderData = user.Order

				// 如果员工之前被软删除，恢复员工
				existingEmployee.DeletedAt = gorm.DeletedAt{}

				if err := tx.Save(&existingEmployee).Error; err != nil {
					tx.Rollback()
					return err
				}

				// 建立员工和部门的关联关系
				if err := w.syncEmployeeDepartments(tx, &existingEmployee, user.Department); err != nil {
					tx.Rollback()
					return err
				}
				logger.Infof("更新员工: %s (%s)", user.Name, user.UserID)
			}
		}
	}

	// 2. 如果表中存在但获取到的列表不存在，软删除表中的数据
	var allEmployees []models.Employee
	if err := tx.Find(&allEmployees).Error; err != nil {
		tx.Rollback()
		return err
	}

	for _, employee := range allEmployees {
		// 如果数据库中的员工在企业微信中不存在，进行软删除
		if !wechatUserIDs[employee.UserID] {
			if err := tx.Delete(&employee).Error; err != nil {
				tx.Rollback()
				return err
			}
			logger.Infof("软删除员工: %s (%s)", employee.Name, employee.UserID)
		}
	}

	return tx.Commit().Error
}

// syncEmployeeDepartments 同步员工和部门的关联关系
func (w *WechatService) syncEmployeeDepartments(tx *gorm.DB, employee *models.Employee, departmentIDs []int) error {
	// 清除现有关联关系
	if err := tx.Model(employee).Association("Departments").Clear(); err != nil {
		return err
	}

	// 建立新的关联关系
	if len(departmentIDs) > 0 {
		var departments []models.Department
		if err := tx.Where("wechat_id IN ?", departmentIDs).Find(&departments).Error; err != nil {
			return err
		}

		if len(departments) > 0 {
			if err := tx.Model(employee).Association("Departments").Append(departments); err != nil {
				return err
			}
		}
	}

	return nil
}

// MessageRequest 发送消息请求结构
type MessageRequest struct {
	ToUser  string      `json:"touser"`
	MsgType string      `json:"msgtype"`
	AgentID int         `json:"agentid"`
	Text    MessageText `json:"text"`
}

// MessageText 文本消息内容
type MessageText struct {
	Content string `json:"content"`
}

// MessageResponse 发送消息响应结构
type MessageResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

// SendMessage 发送企业微信消息
func (w *WechatService) SendMessage(userID, content string) error {
	accessToken, err := w.GetAccessToken()
	if err != nil {
		return err
	}

	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s", accessToken)

	message := MessageRequest{
		ToUser:  userID,
		MsgType: "text",
		AgentID: w.config.AgentID,
		Text: MessageText{
			Content: content,
		},
	}

	messageJSON, err := json.Marshal(message)
	if err != nil {
		return err
	}

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(messageJSON))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	var msgResp MessageResponse
	if err := json.Unmarshal(body, &msgResp); err != nil {
		return err
	}

	if msgResp.ErrCode != 0 {
		return fmt.Errorf("发送消息失败: %s", msgResp.ErrMsg)
	}

	logger.Infof("发送企业微信消息成功: %s", userID)
	return nil
}

// SendAssessmentNotification 发送考核通知
func (w *WechatService) SendAssessmentNotification(userIDs []string, assessmentTitle string) error {
	content := fmt.Sprintf("您有新的考核任务：%s\n请及时完成考核,%s", assessmentTitle, `<a href="https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww2b44aba9c1f20227&redirect_uri=https%3A%2F%2Fkh.yanqu.cn&response_type=code&scope=snsapi_privateinfo&state=STATE&agentid=1000020#wechat_redirect">点击查看</a>`)

	for _, userID := range userIDs {
		if err := w.SendMessage(userID, content); err != nil {
			logger.Errorf("发送考核通知失败 - 用户ID: %s, 错误: %v", userID, err)
			// 继续发送给其他用户，不因为一个用户失败而中断
			continue
		}
	}

	return nil
}

type BasicResponseType struct {
	Errcode     int32
	Errmsg      string
	Userid      string
	User_ticket string
}

func (w *WechatService) GetBasicUser(code string) (*BasicResponseType, error) {
	accessToken, _ := w.GetAccessToken()
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=%s&code=%s", accessToken, code)
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var basicResponseType BasicResponseType
	err = json.Unmarshal(data, &basicResponseType)
	if err != nil {
		return nil, err
	}
	if basicResponseType.Errcode != 0 {
		return nil, errors.New(basicResponseType.Errmsg)
	}

	logger.Infof("basicResponseType:%+v\n", basicResponseType)
	return &basicResponseType, nil
}

type DetailUserInfoResponseType struct {
	Errcode  int32
	Errmsg   string
	Userid   string
	Gender   string
	Avatar   string
	Qr_code  string
	Mobile   string
	Email    string
	Biz_mail string
	Address  string
}

func (w *WechatService) GetDetailUserInfo(code string) (*DetailUserInfoResponseType, error) {
	basicResponseType, err := w.GetBasicUser(code)
	if err != nil {
		return nil, err
	}
	accessToken, _ := w.GetAccessToken()
	url := "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail?access_token=" + accessToken
	bodyData := map[string]string{
		"user_ticket": basicResponseType.User_ticket,
	}
	data, _ := json.Marshal(bodyData)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(data))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	respDataByte, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var respData DetailUserInfoResponseType
	err = json.Unmarshal(respDataByte, &respData)
	if err != nil {
		return nil, err
	}
	if respData.Errcode != 0 {
		return nil, errors.New(respData.Errmsg)
	}
	return &respData, nil
}

type WeComUserInfo struct {
	ErrCode          int      `json:"errcode"`
	ErrMsg           string   `json:"errmsg"`
	UserID           string   `json:"userid"`
	Name             string   `json:"name"`
	Department       []int    `json:"department"`
	Order            []int    `json:"order"`
	Position         string   `json:"position"`
	Mobile           string   `json:"mobile"`
	Gender           string   `json:"gender"`
	Email            string   `json:"email"`
	BizMail          string   `json:"biz_mail"`
	IsLeaderInDept   []int    `json:"is_leader_in_dept"`
	DirectLeader     []string `json:"direct_leader"`
	Avatar           string   `json:"avatar"`
	ThumbAvatar      string   `json:"thumb_avatar"`
	Telephone        string   `json:"telephone"`
	Alias            string   `json:"alias"`
	Address          string   `json:"address"`
	OpenUserID       string   `json:"open_userid"`
	MainDepartment   int      `json:"main_department"`
	Status           int      `json:"status"`
	QrCode           string   `json:"qr_code"`
	ExternalPosition string   `json:"external_position"`
}

func (w *WechatService) FindUserDetail(userId string) (*WeComUserInfo, error) {
	accessToken, err := w.GetAccessToken()
	if err != nil {
		return nil, err
	}
	url := "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + accessToken + "&userid=" + userId

	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	var data WeComUserInfo
	dataByte, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(dataByte, &data)
	if err != nil {
		return nil, err
	}
	if data.ErrCode != 0 {
		return nil, errors.New(data.ErrMsg)
	}

	logger.Infof("获取到的用户信息是%+v\n", data)
	return &data, nil

}
