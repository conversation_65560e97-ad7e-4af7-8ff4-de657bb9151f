package controllers

import (
	"ai_select_admin/logger"
	"ai_select_admin/middleware"
	"ai_select_admin/services"
	"ai_select_admin/utils"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// SSEController SSE控制器
type SSEController struct{}

// NewSSEController 创建SSE控制器
func NewSSEController() *SSEController {
	return &SSEController{}
}

// VideoStatusStream 视频状态更新流
func (sc *SSEController) VideoStatusStream(c *gin.Context) {
	// 检查URL参数中的token进行认证
	token := c.Query("token")
	if token != "" {
		// 临时设置Authorization头部进行认证
		c.Request.Header.Set("Authorization", "Bearer "+token)

		// 执行认证中间件
		middleware.AuthMiddleware()(c)

		// 检查认证是否成功
		if c.IsAborted() {
			return
		}
	}

	// 获取客户端ID，可以从查询参数或生成唯一ID
	clientID := c.Query("client_id")
	if clientID == "" {
		clientID = fmt.Sprintf("client_%d", time.Now().UnixNano())
	}

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 获取SSE服务实例
	sseService := services.GetSSEService()

	// 添加客户端连接
	client := sseService.AddClient(clientID, c)

	// 发送连接成功消息
	welcomeMessage := fmt.Sprintf("data: {\"type\": \"connected\", \"client_id\": \"%s\", \"timestamp\": %d}\n\n",
		clientID, time.Now().Unix())

	// 立即发送欢迎消息
	c.Writer.WriteString(welcomeMessage)
	c.Writer.Flush()

	// 监听客户端断开连接
	notify := c.Writer.CloseNotify()

	// 处理消息发送
	go func() {
		defer func() {
			sseService.RemoveClient(clientID)
			logger.Infof("SSE客户端 %s 连接处理结束", clientID)
		}()

		for {
			select {
			case message, ok := <-client.Channel:
				if !ok || client.IsClosed {
					logger.Infof("SSE客户端 %s 通道已关闭", clientID)
					return
				}

				// 发送消息
				if _, err := c.Writer.WriteString(message); err != nil {
					logger.Errorf("发送SSE消息失败: %v", err)
					return
				}

				// 刷新缓冲区
				if f, ok := c.Writer.(http.Flusher); ok {
					f.Flush()
				}

			case <-notify:
				logger.Infof("SSE客户端 %s 连接断开", clientID)
				return

			case <-time.After(30 * time.Second):
				// 发送心跳消息
				heartbeat := fmt.Sprintf("data: {\"type\": \"heartbeat\", \"timestamp\": %d}\n\n",
					time.Now().Unix())

				if _, err := c.Writer.WriteString(heartbeat); err != nil {
					logger.Errorf("发送心跳消息失败: %v", err)
					return
				}

				if f, ok := c.Writer.(http.Flusher); ok {
					f.Flush()
				}
			}
		}
	}()

	// 保持连接
	<-notify
}

// GetSSEStatus 获取SSE服务状态
func (sc *SSEController) GetSSEStatus(c *gin.Context) {
	sseService := services.GetSSEService()

	status := gin.H{
		"connected_clients": sseService.GetClientCount(),
		"service_status":    "running",
		"timestamp":         time.Now().Unix(),
	}

	utils.Success(c, status)
}

// BroadcastTestMessage 广播测试消息（仅用于测试）
func (sc *SSEController) BroadcastTestMessage(c *gin.Context) {
	var req struct {
		Message string `json:"message" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	sseService := services.GetSSEService()

	// 手动广播测试消息
	sseService.BroadcastVideoStatusUpdate(0, 0, "test", "", "", 0, 0, 0, 0)

	utils.Success(c, gin.H{
		"message": "测试消息已发送",
		"clients": sseService.GetClientCount(),
	})
}
