package h5

import (
	"ai_select_admin/controllers/h5"
	"ai_select_admin/middleware"

	"github.com/gin-gonic/gin"
)

func InitH5Router(r *gin.Engine) {
	userController := &h5.User{}
	examController := &h5.ExamController{}
	h5Api := r.Group("/api/h5")
	{
		h5Api.POST("/login", userController.Login)
	}
	h5WithAuth := r.Group("/api/h5")
	h5WithAuth.Use(middleware.AuthMiddleware())
	{
		h5WithAuth.GET("/getUserInfo", userController.GetUserInfo)
		h5WithAuth.GET("/getCourseList", userController.GetCourseList)
		h5WithAuth.GET("/getCourseDetail/:id", userController.GetCourseDetail)
		h5WithAuth.GET("/getExamList", userController.GetExamList)
		h5WithAuth.POST("/updateVideoProgress", userController.UpdateVideoProgress)

		// 用户统计相关接口
		h5WithAuth.GET("/getLearningStats", userController.GetLearningStats)
		h5WithAuth.GET("/getExamRecords", userController.GetExamRecords)
		h5WithAuth.GET("/getPracticeRecords", userController.GetPracticeRecords)

		// 考试相关接口
		h5WithAuth.POST("/startExam", examController.StartExam)
		h5WithAuth.GET("/examDetail/:id", examController.GetExamDetail)
		h5WithAuth.POST("/saveAnswer", examController.SaveAnswer)
		h5WithAuth.POST("/submitExam", examController.SubmitExam)
		h5WithAuth.GET("/examResult/:id", examController.GetExamResult)
	}

}
