export interface ApiResponse<T = any> {
  code: number
  msg: string
  data?: T
}

export interface PageResponse<T = any>{
  code: number
  msg: string
  data: {
    data: T
    page:PageInfo
  }
}

export interface PageInfo {
  current: number
  size: number
  total: number
  pages: number
}

export interface PageRequest {
  page?: number
  size?: number
  keyword?: string
}
export interface PageInfoResponse<T=any> extends ApiResponse<T> {
  page: PageInfo
}
