<template>
  <div class="exam-result-page">
    <!-- 头部导航 -->
    <div class="result-header">
      <van-icon name="arrow-left" @click="goBack" />
      <div class="header-title">考试报告</div>
      <div class="placeholder"></div>
    </div>
    <!-- 结果概览 -->
    <div class="result-overview" v-if="examResult">
      <div class="top-profile">
        <div class="score-section">
          <!-- Canvas 弧形进度条 -->
          <div class="canvas-container">
            <canvas ref="scoreCanvas" width="200" height="120"></canvas>
            <div class="score-info">
              <div class="score-value">{{ examResult.score }} 分</div>
              <div class="score-total">总分{{ examResult.total_score }}</div>
            </div>
          </div>
        </div>

        <!-- 考试信息 -->
        <div class="exam-info van-hairline--top">
          <div class="info-row">
            <span class="info-label">考试名称：</span>
            <span class="info-value">{{ examResult.title }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">交卷时间：</span>
            <span class="info-value">{{
              formatDate(examResult.submit_time)
            }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">考试用时：</span>
            <span class="info-value">{{
              formatDuration(examResult.duration)
            }}</span>
          </div>
        </div>
      </div>
      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-value">
            {{
              Math.round(
                (examResult.correct_count / examResult.total_count) * 100
              )
            }}%
          </div>
          <div class="stat-label">正确率</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ examResult.correct_count }}</div>
          <div class="stat-label">正确数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">
            {{ examResult.total_count - examResult.correct_count }}
          </div>
          <div class="stat-label">错误数</div>
        </div>
      </div>

      <!-- 答题卡 -->
      <div class="answer-card-section">
        <div class="section-title">答题卡</div>
        <div class="answer-card">
          <div
            v-for="(answer, index) in examResult.answers"
            :key="answer.id"
            class="card-item"
            :class="{
              correct: answer.is_correct,
              wrong: !answer.is_correct,
            }"
            @click="scrollToQuestion(index)"
          >
            {{ index + 1 }}
          </div>
        </div>
        <div class="card-legend">
          <div class="legend-item">
            <div class="legend-dot correct"></div>
            <span>正确</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot wrong"></div>
            <span>错误</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 答题详情 -->
    <div class="answer-details" v-if="examResult">
      <div class="section-title">答题详情</div>
      <div class="answer-list">
        <div
          v-for="(answer, index) in examResult.answers"
          :key="answer.id"
          class="answer-item"
        >
          <div class="answer-header">
            <div class="question-number">{{ index + 1 }}.</div>
            <div class="question-title">{{ answer.question?.content }}</div>
            <div class="answer-status" :class="{ correct: answer.is_correct }">
              <van-icon :name="answer.is_correct ? 'success' : 'close'" />
              <span>{{ answer.is_correct ? "正确" : "错误" }}</span>
            </div>
          </div>

          <!-- <div class="question-content">{{ answer.question?.content }}</div> -->

          <div class="options-container">
            <div
              v-for="option in answer.question?.options"
              :key="option.label"
              class="option-item"
              :class="{
                'user-answer': answer.answer === option.label,
                'correct-answer':
                  answer.question?.correct_answer === option.label,
                'wrong-answer':
                  answer.answer === option.label && !answer.is_correct,
              }"
            >
              <div class="option-label">{{ option.label }}</div>
              <div class="option-content">{{ option.content }}</div>
              <div class="option-status">
                <van-icon
                  v-if="answer.answer === option.label"
                  name="user-o"
                  color="#666"
                />
                <van-icon
                  v-if="answer.question?.correct_answer === option.label"
                  name="success"
                  color="#1bb394"
                />
              </div>
            </div>
          </div>

          <div class="answer-score">
            <span>得分: {{ answer.score }} 分</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import { getExamResult, type ExamResultResponse } from "@/api/user";

const route = useRoute();
const router = useRouter();

const examResult = ref<ExamResultResponse | null>(null);
const loading = ref(false);
const scoreCanvas = ref<HTMLCanvasElement | null>(null);

// 获取考试结果
const fetchExamResult = async () => {
  try {
    loading.value = true;
    const id = Number(route.params.id);
    const response = await getExamResult(id);

    if (response.code === 200 && response.data) {
      examResult.value = response.data;
      // 按题目顺序排序答案
      if (examResult.value.answers) {
        examResult.value.answers.sort((a, b) => a.question_id - b.question_id);
      }
      // 绘制canvas
      nextTick(() => {
        drawScoreArc();
      });
    } else {
      showToast(response.msg || "获取考试结果失败");
      goBack();
    }
  } catch (error) {
    console.error("获取考试结果失败:", error);
    showToast("获取考试结果失败");
    goBack();
  } finally {
    loading.value = false;
  }
};

// 绘制分数弧形
const drawScoreArc = () => {
  if (!scoreCanvas.value || !examResult.value) return;

  const canvas = scoreCanvas.value;
  const ctx = canvas.getContext("2d");
  if (!ctx) return;

  const centerX = canvas.width / 2;
  const centerY = canvas.height - 20;
  const radius = 80;
  const startAngle = Math.PI;
  const endAngle = 0;

  const percentage = examResult.value.score / examResult.value.total_score;
  const currentAngle = -(startAngle + (endAngle - startAngle) * percentage);

  // 清除画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 绘制背景弧
  ctx.beginPath();
  ctx.arc(centerX, centerY, radius, startAngle, endAngle);
  ctx.strokeStyle = "#CDF3E7";
  ctx.lineWidth = 8;
  ctx.lineCap = "round";
  ctx.stroke();
  // 正确计算当前进度点的位置
  const currentX = centerX + radius * Math.cos(currentAngle);
  const currentY = centerY + radius * Math.sin(currentAngle);
  ctx.beginPath();
  ctx.arc(currentX, currentY, 8, 0, Math.PI * 2);
  ctx.fillStyle = "#16AC8E";
  ctx.fill();
  ctx.strokeStyle = "#ffffff"; // 白色边框
  ctx.lineWidth = 4;
  ctx.stroke();
};

// 滚动到指定题目
const scrollToQuestion = (index: number) => {
  const element = document.querySelector(
    `.answer-item:nth-child(${index + 1})`
  );
  if (element) {
    element.scrollIntoView({ behavior: "smooth", block: "start" });
  }
};

// 格式化时长
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`;
  } else {
    return `${secs}秒`;
  }
};

// 格式化日期
const formatDate = (dateStr?: string): string => {
  if (!dateStr) return "-";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 返回
const goBack = () => {
  router.push("/exam");
};

onMounted(() => {
  fetchExamResult();
});
</script>

<style scoped>
.exam-result-page {
  min-height: 100vh;
  background-color: #f6f7fb;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.placeholder {
  width: 24px;
}

.result-overview {
  margin: 12px;
  border-radius: 12px;
}

.score-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.canvas-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-info {
  position: absolute;
  top: 64%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #333;
}

.score-value {
  font-size: 16px;
  font-weight: bold;
  color: #5a5a5a;
}

.score-total {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}
.top-profile {
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.exam-info {
  padding: 16px;
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  font-size: 12px;
}

.info-value {
  color: #5a5a5a;
  font-size: 12px;
  font-weight: 500;
}

.stats-section {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #5a5a5a;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #777;
}

.answer-card-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #5a5a5a;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  width: 7px;
  height: 16px;
  background-color: #1bb394;
  margin-right: 8px;
  border-radius: 0px 6px 0px 6px;
}

.answer-card {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  margin-bottom: 16px;
  padding: 0px 12px;
  padding-top: 10px;
}

.card-item {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card-item.correct {
  background-color: #1bb394;
  color: #fff;
}

.card-item.wrong {
  background-color: #ff4d4f;
  color: #fff;
}

.card-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-dot.correct {
  background-color: #1bb394;
}

.legend-dot.wrong {
  background-color: #ff4d4f;
}

.answer-details {
  margin: 12px;
  padding-bottom: 100px;
}

.answer-details .section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.answer-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.answer-header {
  display: flex;
  position: relative;
  margin-bottom: 12px;
  padding-right: 48px;
}

.question-number {
  color: #1bb394;
  font-weight: bold;
  margin-right: 8px;
}

.question-title {
  flex: 1;
  font-weight: bold;
  color: #5a5a5a;
  font-size: 13px;
}

.answer-status {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #ff4d4f;
  font-size: 14px;
}

.answer-status.correct {
  color: #1bb394;
}

.question-content {
  color: #646566;
  margin-bottom: 12px;
  line-height: 1.5;
  font-size: 12px;
}

.options-container {
  margin-bottom: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  position: relative;
  font-size: 12px;
}

.user-answer {
  background-color: rgba(102, 102, 102, 0.1);
  border-color: #666;
}

.correct-answer {
  background-color: rgba(27, 179, 148, 0.1);
  border-color: #1bb394;
  border: 1px solid #1bb394;
  .option-label {
    background-color: #1bb394;
    color: #fff;
  }
}

.wrong-answer {
  background-color: rgba(255, 77, 79, 0.1);
  border-color: #ff4d4f;
  border: 1px solid #ff4d4f;
  .option-label {
    background-color: #ff4d4f;
    color: #fff;
  }
}

.option-label {
  width: 24px;
  height: 24px;
  background-color: #f2f3f5;
  color: #323233;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-weight: bold;
}

.option-content {
  flex: 1;
}

.option-status {
  display: flex;
  gap: 4px;
}

.answer-score {
  text-align: right;
  color: #1bb394;
  font-weight: bold;
  font-size: 14px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #ebedf0;
}
</style>
