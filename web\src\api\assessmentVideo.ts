import request from '@/utils/request'
import type {
  AssessmentVideo,
  AssessmentVideoListRequest,
  AssessmentVideoCreateRequest,
  AssessmentVideoUpdateRequest
} from '@/types/assessmentVideo'
import type { ApiResponse, PageInfo } from '@/types/common'

interface PageResponse<T=any> extends ApiResponse<T> {
  page: PageInfo
}

// 考核视频搜索查询参数（用于下拉选择）
export interface AssessmentVideoSearchParams {
  page?: number;
  page_size?: number;
  keyword?: string;
  category_id?: number;
}

// 获取考核视频列表
export function getAssessmentVideoList(params: AssessmentVideoListRequest): Promise<PageResponse<AssessmentVideo[]>> {
  return request({
    url: '/admin/assessment-videos',
    method: 'get',
    params
  })
}

// 搜索考核视频（用于下拉选择）
export function searchAssessmentVideos(params: AssessmentVideoSearchParams): Promise<PageResponse<AssessmentVideo[]>> {
  return request({
    url: '/admin/assessment-videos/search',
    method: 'get',
    params
  })
}

// 获取考核视频详情
export function getAssessmentVideo(id: number) {
  return request({
    url: `/admin/assessment-videos/${id}`,
    method: 'get'
  })
}

// 创建考核视频
export function createAssessmentVideo(data: AssessmentVideoCreateRequest) {
  return request({
    url: '/admin/assessment-videos',
    method: 'post',
    data
  })
}

// 更新考核视频
export function updateAssessmentVideo(id: number, data: AssessmentVideoUpdateRequest) {
  return request({
    url: `/admin/assessment-videos/${id}`,
    method: 'put',
    data
  })
}

// 删除考核视频
export function deleteAssessmentVideo(id: number) {
  return request({
    url: `/admin/assessment-videos/${id}`,
    method: 'delete'
  })
}
