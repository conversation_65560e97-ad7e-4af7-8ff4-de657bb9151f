import request from '@/utils/request'
import type { ApiResponse, PageInfo } from '@/types/common'

// 考核记录状态
export const AssessmentRecordStatus = {
  NOT_STARTED: 0, // 未开始
  IN_PROGRESS: 1, // 进行中
  COMPLETED: 2,   // 已完成
  EXPIRED: 3      // 已过期
} as const;

export type AssessmentRecordStatus =
  typeof AssessmentRecordStatus[keyof typeof AssessmentRecordStatus];

// 考核记录接口
export interface AssessmentRecord {
  id: number
  employee_id: number
  assessment_video_id: number
  status: AssessmentRecordStatus
  status_text: string
  assigned_at: string
  started_at?: string
  completed_at?: string
  exam_start_time?: string
  score?: number
  video_progress: number
  exam_score?: number
  exam_status: number
  exam_status_text: string
  practice_count: number
  is_passed?: boolean
  pass_score?: number
  is_passed_text: string
  employee?: {
    id: number
    name: string
    position: string
    mobile: string
    avatar: string
    departments?: Array<{
      id: number
      name: string
    }>
  }
  assessment_video?: {
    id: number
    title: string
    description: string
    category?: {
      id: number
      name: string
    }
    exam?: {
      id: number
      title: string
      type: number
    }
  }
  created_at: string
  updated_at: string
}

// 考核记录列表请求参数
export interface AssessmentRecordListRequest {
  page?: number
  page_size?: number
  employee_name?: string
  assessment_video_title?: string
  department_id?: number
  status?: number
  employee_id?: number
  assessment_video_id?: number|undefined
  is_passed?: number // 是否及格筛选 0不及格 1及格 2未考试
}

// 创建考核记录请求
export interface AssessmentRecordCreateRequest {
  employee_ids: number[]
  assessment_video_id: number|undefined
  exam_start_time?: string
}

// 更新考核记录请求
export interface AssessmentRecordUpdateRequest {
  status?: AssessmentRecordStatus
  video_progress?: number
  exam_score?: number
  exam_start_time?: string
}

interface PageResponse<T = any> extends ApiResponse<T> {
  page: PageInfo
}

// 获取考核记录列表
export function getAssessmentRecordList(params: AssessmentRecordListRequest): Promise<PageResponse<AssessmentRecord[]>> {
  return request({
    url: '/admin/assessment-records',
    method: 'get',
    params
  })
}

// 获取考核记录详情
export function getAssessmentRecord(id: number): Promise<ApiResponse<AssessmentRecord>> {
  return request({
    url: `/admin/assessment-records/${id}`,
    method: 'get'
  })
}

// 创建考核记录
export function createAssessmentRecord(data: AssessmentRecordCreateRequest): Promise<ApiResponse<any>> {
  return request({
    url: '/admin/assessment-records',
    method: 'post',
    data
  })
}

// 更新考核记录
export function updateAssessmentRecord(id: number, data: AssessmentRecordUpdateRequest): Promise<ApiResponse<AssessmentRecord>> {
  return request({
    url: `/admin/assessment-records/${id}`,
    method: 'put',
    data
  })
}

// 删除考核记录
export function deleteAssessmentRecord(id: number): Promise<ApiResponse<any>> {
  return request({
    url: `/admin/assessment-records/${id}`,
    method: 'delete'
  })
}

// 获取考试详情
export function getExamDetail(id: number): Promise<ApiResponse<any>> {
  return request({
    url: `/admin/assessment-records/exam-detail/${id}`,
    method: 'get'
  })
}
