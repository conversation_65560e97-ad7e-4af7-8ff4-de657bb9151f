import type { AssessmentCategory } from './assessmentCategory'
import type { Exam } from './exam'
import type { Video } from './video'
import type { Teacher } from './teacher'

// 考核视频关联
export interface AssessmentVideoRelation {
  id: number
  assessment_video_id: number
  video_id: number
  sort: number
  video?: Video
}

// 考核视频教师关联
export interface AssessmentVideoTeacher {
  id: number
  assessment_video_id: number
  teacher_id: number
  teacher?: Teacher
}

// 考核视频刷题试卷关联
export interface AssessmentVideoPractice {
  id: number
  assessment_video_id: number
  exam_id: number
  exam?: Exam
}

// 考核视频
export interface AssessmentVideo {
  id: number
  title: string
  description: string
  category_id: number
  exam_id: number
  status: number
  video_count: number
  teacher_count: number
  practice_exam_count: number
  category?: AssessmentCategory
  exam?: Exam
  videos?: AssessmentVideoRelation[]
  teachers?: AssessmentVideoTeacher[]
  practice_exams?: AssessmentVideoPractice[]
  created_at: string
  updated_at: string
}

// 考核视频列表请求参数
export interface AssessmentVideoListRequest {
  page?: number
  page_size?: number
  title?: string
  category_id?: number
  status?: number
}

// 创建考核视频请求参数
export interface AssessmentVideoCreateRequest {
  title: string
  description?: string
  category_id?: number
  exam_id?: number
  video_ids: number[]
  teacher_ids?: number[]
  practice_exam_ids?: number[]
  status?: number
  create_makeup_exam?: boolean // 前端特有字段，用于控制是否同时创建补考
}

// 更新考核视频请求参数
export interface AssessmentVideoUpdateRequest {
  title: string
  description?: string
  category_id?: number
  exam_id?: number
  video_ids: number[]
  teacher_ids?: number[]
  practice_exam_ids?: number[]
  status?: number
}

// 视频选择选项
export interface VideoSelectOption {
  id: number
  title: string
  cover_image: string
  duration: number
  category_name: string
  selected?: boolean
}
