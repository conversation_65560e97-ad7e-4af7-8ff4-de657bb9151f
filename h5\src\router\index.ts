import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/course'
    },
    {
      path: '/course',
      name: 'Course',
      component: () => import('@/views/Course.vue'),
      meta: {
        title: '课程'
      }
    },
    {
      path: '/course/play/:id',
      name: 'CoursePlay',
      component: () => import('@/views/CoursePlay.vue'),
      meta: {
        title: '课程播放'
      }
    },
    {
      path: '/exam',
      name: 'Exam',
      component: () => import('@/views/Exam.vue'),
      meta: {
        title: '考试'
      }
    },
    {
      path: '/exam/taking/:id',
      name: 'ExamTaking',
      component: () => import('@/views/ExamTaking.vue'),
      meta: {
        title: '考试中'
      }
    },
    {
      path: '/exam/result/:id',
      name: 'ExamResult',
      component: () => import('@/views/ExamResult.vue'),
      meta: {
        title: '考试结果'
      }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('@/views/Profile.vue'),
      meta: {
        title: '我的'
      }
    },
    {
      path: '/error',
      name: 'Error',
      component: () => import('@/views/Error.vue'),
      meta: {
        title: '错误'
      }
    },
  ]
})

export default router
