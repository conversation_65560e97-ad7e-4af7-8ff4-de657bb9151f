// 模拟数据，用于开发测试
export const mockCourseCategories = [
  { id: 1, name: '技术培训', parent_id: 0, sort: 1, created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' },
  { id: 2, name: '安全培训', parent_id: 0, sort: 2, created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' },
  { id: 3, name: '管理培训', parent_id: 0, sort: 3, created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' }
]

export const mockCourses = [
  {
    id: 1,
    title: '课程主题一',
    description: '课程描述一',
    cover_image: '',
    category_id: 1,
    category_name: '技术培训',
    teacher_names: ['张老师', '李老师', '王老师'],
    teacher_avatars: [
      'https://img.yzcdn.cn/vant/cat.jpeg',
      'https://img.yzcdn.cn/vant/cat.jpeg',
      'https://img.yzcdn.cn/vant/cat.jpeg'
    ],
    created_at: '2024-01-01T10:30:00Z',
    updated_at: '2024-01-01T10:30:00Z'
  },
  {
    id: 2,
    title: '课程主题二',
    description: '课程描述二',
    cover_image: '',
    category_id: 2,
    category_name: '安全培训',
    teacher_names: ['赵老师', '钱老师', '孙老师'],
    teacher_avatars: [
      'https://img.yzcdn.cn/vant/cat.jpeg',
      'https://img.yzcdn.cn/vant/cat.jpeg',
      'https://img.yzcdn.cn/vant/cat.jpeg'
    ],
    created_at: '2024-01-02T14:20:00Z',
    updated_at: '2024-01-02T14:20:00Z'
  }
]

export const mockPaperCategories = [
  { id: 1, name: '政治', parent_id: 0, sort: 1, created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' },
  { id: 2, name: '英语', parent_id: 0, sort: 2, created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' },
  { id: 3, name: '数学', parent_id: 0, sort: 3, created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' },
  { id: 4, name: '专业课', parent_id: 0, sort: 4, created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' }
]

export const mockPapers = [
  // 考试试卷
  {
    id: 1,
    title: '2025年研究生政治期末考试',
    description: '政治期末考试',
    type: 'exam',
    category_id: 1,
    category_name: '政治',
    question_count: 50,
    created_at: '2024-01-01T10:30:00Z',
    updated_at: '2024-01-01T10:30:00Z'
  },
  {
    id: 2,
    title: '2025年研究生英语期末考试',
    description: '英语期末考试',
    type: 'exam',
    category_id: 2,
    category_name: '英语',
    question_count: 80,
    created_at: '2024-01-02T14:20:00Z',
    updated_at: '2024-01-02T14:20:00Z'
  },
  {
    id: 3,
    title: '2025年研究生数学期末考试',
    description: '数学期末考试',
    type: 'exam',
    category_id: 3,
    category_name: '数学',
    question_count: 60,
    created_at: '2024-01-03T09:15:00Z',
    updated_at: '2024-01-03T09:15:00Z'
  },
  // 刷题试卷
  {
    id: 4,
    title: '2025年研究生政治模拟题库',
    description: '政治模拟题库',
    type: 'practice',
    category_id: 1,
    category_name: '政治',
    question_count: 100,
    created_at: '2024-01-04T16:45:00Z',
    updated_at: '2024-01-04T16:45:00Z'
  },
  {
    id: 5,
    title: '2025年研究生英语模拟题库',
    description: '英语模拟题库',
    type: 'practice',
    category_id: 2,
    category_name: '英语',
    question_count: 120,
    created_at: '2024-01-05T11:20:00Z',
    updated_at: '2024-01-05T11:20:00Z'
  },
  {
    id: 6,
    title: '2025年研究生数学模拟题库',
    description: '数学模拟题库',
    type: 'practice',
    category_id: 3,
    category_name: '数学',
    question_count: 90,
    created_at: '2024-01-06T13:30:00Z',
    updated_at: '2024-01-06T13:30:00Z'
  },
  {
    id: 7,
    title: '2025年研究生专业课模拟题库',
    description: '专业课模拟题库',
    type: 'practice',
    category_id: 4,
    category_name: '专业课',
    question_count: 150,
    created_at: '2024-01-07T15:45:00Z',
    updated_at: '2024-01-07T15:45:00Z'
  }
]
