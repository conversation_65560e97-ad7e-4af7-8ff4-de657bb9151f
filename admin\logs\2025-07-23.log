{"file":"G:/code/go/hr_course/admin/logger/logger.go:62","func":"ai_select_admin/logger.Info","level":"info","msg":"redis 连接成功","time":"2025-07-23 08:44:44"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:77","func":"ai_select_admin/logger.Errorf","level":"error","msg":"查询管理员列表失败: invalid connection","time":"2025-07-23 08:47:25"}
{"body_size":33,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":100845700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-23 08:47:25","timestamp":"2025-07-23 08:47:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"管理员登录成功: admin","time":"2025-07-23 08:47:35"}
{"body_size":619,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":71902500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-23 08:47:35","timestamp":"2025-07-23 08:47:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":717,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":16708200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-23 08:47:35","timestamp":"2025-07-23 08:47:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":115684200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:47:41","timestamp":"2025-07-23 08:47:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":114547000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-23 08:47:41","timestamp":"2025-07-23 08:47:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":27191100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-23 08:47:42","timestamp":"2025-07-23 08:47:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"SSE客户端连接: video_client_1753231662828, 当前连接数: 1","time":"2025-07-23 08:47:42"}
{"body_size":3144,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":145075700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-23 08:47:43","timestamp":"2025-07-23 08:47:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":97,"client_ip":"127.0.0.1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":4215227000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/sse/video-status?client_id=video_client_1753231662828\u0026token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwidXNlcl90eXBlIjoiYWRtaW4iLCJpc3MiOiJocl9hZG1pbiIsImV4cCI6MTc1MzMxODA1NSwibmJmIjoxNzUzMjMxNjU1LCJpYXQiOjE3NTMyMzE2NTV9.ZWIjiQeSrHD1ncozMAqMLdDJbDtG8-zS0_6q5bBJVQQ","status_code":200,"time":"2025-07-23 08:47:47","timestamp":"2025-07-23 08:47:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":730,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":103939900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/departments/tree","status_code":200,"time":"2025-07-23 08:47:47","timestamp":"2025-07-23 08:47:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":12814,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":266086300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees?page=1\u0026page_size=20\u0026name=","status_code":200,"time":"2025-07-23 08:47:47","timestamp":"2025-07-23 08:47:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"查询考核视频列表成功: [{10 07-21测试补考 测试 1 4 1 2025-07-21 19:02:33.043 +0800 CST 2025-07-21 19:02:33.043 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0002d8cf0 0xc0000f8840 [] [] []} {9 07-21测试 测试 1 4 1 2025-07-21 19:02:32.79 +0800 CST 2025-07-21 19:02:32.79 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0002d8cf0 0xc0000f8840 [{25 9 9 1 2025-07-21 19:02:32.823 +0800 CST 2025-07-21 19:02:32.823 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d500} {26 9 8 2 2025-07-21 19:02:32.825 +0800 CST 2025-07-21 19:02:32.825 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d400} {27 9 6 3 2025-07-21 19:02:32.856 +0800 CST 2025-07-21 19:02:32.856 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d300} {28 9 4 4 2025-07-21 19:02:32.871 +0800 CST 2025-07-21 19:02:32.871 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d200}] [] []} {6 补考 补考 1 3 1 2025-07-21 18:08:59.892 +0800 CST 2025-07-21 18:08:59.892 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0002d8cf0 0xc0000f8780 [{17 6 6 1 2025-07-21 18:08:59.912 +0800 CST 2025-07-21 18:08:59.912 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d300} {18 6 8 2 2025-07-21 18:08:59.913 +0800 CST 2025-07-21 18:08:59.913 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d400} {19 6 9 3 2025-07-21 18:08:59.914 +0800 CST 2025-07-21 18:08:59.914 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d500} {20 6 4 4 2025-07-21 18:08:59.915 +0800 CST 2025-07-21 18:08:59.915 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d200}] [] []} {5 测试7-16 测试数据 1 3 1 2025-07-16 10:21:04.553 +0800 CST 2025-07-17 14:57:03.685 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0002d8cf0 0xc0000f8780 [{15 5 6 1 2025-07-17 14:57:03.797 +0800 CST 2025-07-17 14:57:03.797 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d300} {16 5 4 2 2025-07-17 14:57:03.826 +0800 CST 2025-07-17 14:57:03.826 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d200}] [] []} {4 测试考核 测试数据 1 10 1 2025-07-15 18:01:34.556 +0800 CST 2025-07-15 18:01:34.556 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0002d8cf0 0xc0000f8900 [{7 4 4 1 2025-07-15 18:01:34.561 +0800 CST 2025-07-15 18:01:34.561 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d200} {8 4 6 2 2025-07-15 18:01:34.563 +0800 CST 2025-07-15 18:01:34.563 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d300}] [] []}]","time":"2025-07-23 08:47:47"}
{"body_size":12510,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":436182000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=100","status_code":200,"time":"2025-07-23 08:47:47","timestamp":"2025-07-23 08:47:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1755,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":569266200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-records?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:47:47","timestamp":"2025-07-23 08:47:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"删除考核记录成功: %!s(uint=11)","time":"2025-07-23 08:47:50"}
{"body_size":33,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":38360700,"level":"info","method":"DELETE","msg":"HTTP Request","path":"/api/admin/assessment-records/11","status_code":200,"time":"2025-07-23 08:47:50","timestamp":"2025-07-23 08:47:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":15343800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-records?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:47:50","timestamp":"2025-07-23 08:47:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":12104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":26915300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:47:52","timestamp":"2025-07-23 08:47:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":2104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":9330700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026department_id=3\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:47:56","timestamp":"2025-07-23 08:47:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":2104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":26886600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026department_id=3\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:47:56","timestamp":"2025-07-23 08:47:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:77","func":"ai_select_admin/logger.Errorf","level":"error","msg":"发送心跳消息失败: http: wrote more than the declared Content-Length","time":"2025-07-23 08:48:13"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"SSE客户端断开: video_client_1753231662828, 当前连接数: 0","time":"2025-07-23 08:48:13"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"SSE客户端 video_client_1753231662828 连接处理结束","time":"2025-07-23 08:48:13"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"管理员登录成功: admin","time":"2025-07-23 08:48:18"}
{"body_size":620,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":17753200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-23 08:48:18","timestamp":"2025-07-23 08:48:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":717,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":4139800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-23 08:48:19","timestamp":"2025-07-23 08:48:19","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":11919900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-records?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:48:21","timestamp":"2025-07-23 08:48:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":730,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":27086100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/departments/tree","status_code":200,"time":"2025-07-23 08:48:21","timestamp":"2025-07-23 08:48:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":12814,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":87972900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees?page=1\u0026page_size=20\u0026name=","status_code":200,"time":"2025-07-23 08:48:21","timestamp":"2025-07-23 08:48:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"查询考核视频列表成功: [{10 07-21测试补考 测试 1 4 1 2025-07-21 19:02:33.043 +0800 CST 2025-07-21 19:02:33.043 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000140ab0 0xc000248840 [] [] []} {9 07-21测试 测试 1 4 1 2025-07-21 19:02:32.79 +0800 CST 2025-07-21 19:02:32.79 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000140ab0 0xc000248840 [{25 9 9 1 2025-07-21 19:02:32.823 +0800 CST 2025-07-21 19:02:32.823 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d700} {26 9 8 2 2025-07-21 19:02:32.825 +0800 CST 2025-07-21 19:02:32.825 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d500} {27 9 6 3 2025-07-21 19:02:32.856 +0800 CST 2025-07-21 19:02:32.856 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d400} {28 9 4 4 2025-07-21 19:02:32.871 +0800 CST 2025-07-21 19:02:32.871 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d200}] [] []} {6 补考 补考 1 3 1 2025-07-21 18:08:59.892 +0800 CST 2025-07-21 18:08:59.892 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000140ab0 0xc000248780 [{17 6 6 1 2025-07-21 18:08:59.912 +0800 CST 2025-07-21 18:08:59.912 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d400} {18 6 8 2 2025-07-21 18:08:59.913 +0800 CST 2025-07-21 18:08:59.913 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d500} {19 6 9 3 2025-07-21 18:08:59.914 +0800 CST 2025-07-21 18:08:59.914 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d700} {20 6 4 4 2025-07-21 18:08:59.915 +0800 CST 2025-07-21 18:08:59.915 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d200}] [] []} {5 测试7-16 测试数据 1 3 1 2025-07-16 10:21:04.553 +0800 CST 2025-07-17 14:57:03.685 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000140ab0 0xc000248780 [{15 5 6 1 2025-07-17 14:57:03.797 +0800 CST 2025-07-17 14:57:03.797 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d400} {16 5 4 2 2025-07-17 14:57:03.826 +0800 CST 2025-07-17 14:57:03.826 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d200}] [] []} {4 测试考核 测试数据 1 10 1 2025-07-15 18:01:34.556 +0800 CST 2025-07-15 18:01:34.556 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000140ab0 0xc000248900 [{7 4 4 1 2025-07-15 18:01:34.561 +0800 CST 2025-07-15 18:01:34.561 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d200} {8 4 6 2 2025-07-15 18:01:34.563 +0800 CST 2025-07-15 18:01:34.563 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00012d400}] [] []}]","time":"2025-07-23 08:48:21"}
{"body_size":12510,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":139007300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=100","status_code":200,"time":"2025-07-23 08:48:21","timestamp":"2025-07-23 08:48:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":12104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":44617500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:48:22","timestamp":"2025-07-23 08:48:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":2104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":83273200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026department_id=3\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:48:25","timestamp":"2025-07-23 08:48:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":3633,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":8602300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos/search?page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:48:32","timestamp":"2025-07-23 08:48:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":3633,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":4442200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos/search?page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:48:32","timestamp":"2025-07-23 08:48:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"批量创建考核记录成功: 1条","time":"2025-07-23 08:48:37"}
{"body_size":52,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":21341100,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/assessment-records","status_code":200,"time":"2025-07-23 08:48:37","timestamp":"2025-07-23 08:48:37","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1758,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":19115600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-records?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:48:37","timestamp":"2025-07-23 08:48:37","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"发送企业微信消息成功: ChenZhaoYu","time":"2025-07-23 08:48:38"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"考核通知发送成功，通知用户数: 1","time":"2025-07-23 08:48:38"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户成功\u0026{ID:0 UserID: Name: Position: Mobile: Gender: Email: Avatar: Telephone: Alias: Address: OpenUserID: MainDeptID:0 Status:0 IsLeaderInDept: IsLeaderData:[] Department: DepartmentData:[] Order: OrderData:[] CreatedAt:0001-01-01 00:00:00 +0000 UTC UpdatedAt:0001-01-01 00:00:00 +0000 UTC DeletedAt:{Time:0001-01-01 00:00:00 +0000 UTC Valid:false} Departments:[]}\n","time":"2025-07-23 08:52:33"}
{"body_size":385,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":5266200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getUserInfo","status_code":200,"time":"2025-07-23 08:52:33","timestamp":"2025-07-23 08:52:33","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"**************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getCourseList?page=1\u0026total=0\u0026pageSize=10\u0026pages=0","status_code":401,"time":"2025-07-23 08:52:35","timestamp":"2025-07-23 08:52:35","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户成功\u0026{ID:41 UserID:ChenZhaoYu Name:陈兆雨 Position: Mobile:13083409461 Gender:1 Email: Avatar:https://wework.qpic.cn/wwpic3az/218437_3f4SXnA4RhyZPcl_1707498418/0 Telephone: Alias: Address: OpenUserID: MainDeptID:3 Status:1 IsLeaderInDept:[0] IsLeaderData:[0] Department:[3] DepartmentData:[3] Order:[0] OrderData:[0] CreatedAt:2025-07-15 17:12:47.938 +0800 CST UpdatedAt:2025-07-22 18:09:03.336 +0800 CST DeletedAt:{Time:0001-01-01 00:00:00 +0000 UTC Valid:false} Departments:[{ID:5 WechatID:3 Name:设计研发部 NameEn: ParentID:1 Sort:99999000 Status:1 CreatedAt:2025-07-08 17:01:18.767 +0800 CST UpdatedAt:2025-07-15 17:38:02.663 +0800 CST DeletedAt:{Time:0001-01-01 00:00:00 +0000 UTC Valid:false} Children:[]}]}\n","time":"2025-07-23 08:52:47"}
{"body_size":662,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":50971400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getUserInfo","status_code":200,"time":"2025-07-23 08:52:47","timestamp":"2025-07-23 08:52:47","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":1878,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":46843800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getCourseList?page=1\u0026total=0\u0026pageSize=10\u0026pages=0","status_code":200,"time":"2025-07-23 08:52:47","timestamp":"2025-07-23 08:52:47","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户 41 的试卷列表","time":"2025-07-23 08:52:49"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"用户 41 的考核记录数量: 1\n","time":"2025-07-23 08:52:49"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"返回试卷列表 - 考试试卷: 1, 刷题试卷: 3","time":"2025-07-23 08:52:49"}
{"body_size":1676,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":43426900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamList","status_code":200,"time":"2025-07-23 08:52:49","timestamp":"2025-07-23 08:52:49","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":113,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":11673400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/h5/startExam","status_code":200,"time":"2025-07-23 08:52:52","timestamp":"2025-07-23 08:52:52","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":36,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":38227100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examDetail/8","status_code":200,"time":"2025-07-23 08:52:53","timestamp":"2025-07-23 08:52:53","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户 41 的试卷列表","time":"2025-07-23 08:52:53"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"用户 41 的考核记录数量: 1\n","time":"2025-07-23 08:52:53"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"返回试卷列表 - 考试试卷: 1, 刷题试卷: 3","time":"2025-07-23 08:52:53"}
{"body_size":1676,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":19756500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamList","status_code":200,"time":"2025-07-23 08:52:53","timestamp":"2025-07-23 08:52:53","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":100,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":16906300,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/h5/startExam","status_code":200,"time":"2025-07-23 08:52:57","timestamp":"2025-07-23 08:52:57","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":8989,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":14004000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examDetail/9","status_code":200,"time":"2025-07-23 08:52:57","timestamp":"2025-07-23 08:52:57","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":78,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":419849400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/h5/submitExam","status_code":200,"time":"2025-07-23 08:53:14","timestamp":"2025-07-23 08:53:14","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":11308,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":26180200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examResult/9","status_code":200,"time":"2025-07-23 08:53:15","timestamp":"2025-07-23 08:53:15","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":948,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":39617700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getPracticeRecords","status_code":200,"time":"2025-07-23 08:53:18","timestamp":"2025-07-23 08:53:18","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":499,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":104924600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamRecords","status_code":200,"time":"2025-07-23 08:53:18","timestamp":"2025-07-23 08:53:18","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":103,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":149038900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getLearningStats","status_code":200,"time":"2025-07-23 08:53:18","timestamp":"2025-07-23 08:53:18","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户 41 的试卷列表","time":"2025-07-23 08:53:23"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"用户 41 的考核记录数量: 1\n","time":"2025-07-23 08:53:23"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"返回试卷列表 - 考试试卷: 1, 刷题试卷: 3","time":"2025-07-23 08:53:23"}
{"body_size":1676,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":81470000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamList","status_code":200,"time":"2025-07-23 08:53:23","timestamp":"2025-07-23 08:53:23","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getCourseList?page=1\u0026total=0\u0026pageSize=10\u0026pages=0","status_code":401,"time":"2025-07-23 08:53:41","timestamp":"2025-07-23 08:53:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":717,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":9236500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-23 08:53:48","timestamp":"2025-07-23 08:53:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":730,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":15683700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/departments/tree","status_code":200,"time":"2025-07-23 08:53:51","timestamp":"2025-07-23 08:53:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":6513,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":122161600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:53:51","timestamp":"2025-07-23 08:53:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":730,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":5421300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/departments/tree","status_code":200,"time":"2025-07-23 08:53:53","timestamp":"2025-07-23 08:53:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":12814,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":50978400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees?page=1\u0026page_size=20\u0026name=","status_code":200,"time":"2025-07-23 08:53:53","timestamp":"2025-07-23 08:53:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"查询考核视频列表成功: [{10 07-21测试补考 测试 1 4 1 2025-07-21 19:02:33.043 +0800 CST 2025-07-21 19:02:33.043 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000610750 0xc00061e240 [] [] []} {9 07-21测试 测试 1 4 1 2025-07-21 19:02:32.79 +0800 CST 2025-07-21 19:02:32.79 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000610750 0xc00061e240 [{25 9 9 1 2025-07-21 19:02:32.823 +0800 CST 2025-07-21 19:02:32.823 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057cc00} {26 9 8 2 2025-07-21 19:02:32.825 +0800 CST 2025-07-21 19:02:32.825 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057cb00} {27 9 6 3 2025-07-21 19:02:32.856 +0800 CST 2025-07-21 19:02:32.856 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057ca00} {28 9 4 4 2025-07-21 19:02:32.871 +0800 CST 2025-07-21 19:02:32.871 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057c800}] [] []} {6 补考 补考 1 3 1 2025-07-21 18:08:59.892 +0800 CST 2025-07-21 18:08:59.892 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000610750 0xc00061e180 [{17 6 6 1 2025-07-21 18:08:59.912 +0800 CST 2025-07-21 18:08:59.912 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057ca00} {18 6 8 2 2025-07-21 18:08:59.913 +0800 CST 2025-07-21 18:08:59.913 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057cb00} {19 6 9 3 2025-07-21 18:08:59.914 +0800 CST 2025-07-21 18:08:59.914 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057cc00} {20 6 4 4 2025-07-21 18:08:59.915 +0800 CST 2025-07-21 18:08:59.915 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057c800}] [] []} {5 测试7-16 测试数据 1 3 1 2025-07-16 10:21:04.553 +0800 CST 2025-07-17 14:57:03.685 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000610750 0xc00061e180 [{15 5 6 1 2025-07-17 14:57:03.797 +0800 CST 2025-07-17 14:57:03.797 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057ca00} {16 5 4 2 2025-07-17 14:57:03.826 +0800 CST 2025-07-17 14:57:03.826 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057c800}] [] []} {4 测试考核 测试数据 1 10 1 2025-07-15 18:01:34.556 +0800 CST 2025-07-15 18:01:34.556 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc000610750 0xc00061e300 [{7 4 4 1 2025-07-15 18:01:34.561 +0800 CST 2025-07-15 18:01:34.561 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057c800} {8 4 6 2 2025-07-15 18:01:34.563 +0800 CST 2025-07-15 18:01:34.563 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc00057ca00}] [] []}]","time":"2025-07-23 08:53:53"}
{"body_size":12510,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":100968200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=100","status_code":200,"time":"2025-07-23 08:53:53","timestamp":"2025-07-23 08:53:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1756,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":117162800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-records?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:53:53","timestamp":"2025-07-23 08:53:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户成功\u0026{ID:41 UserID:ChenZhaoYu Name:陈兆雨 Position: Mobile:13083409461 Gender:1 Email: Avatar:https://wework.qpic.cn/wwpic3az/218437_3f4SXnA4RhyZPcl_1707498418/0 Telephone: Alias: Address: OpenUserID: MainDeptID:3 Status:1 IsLeaderInDept:[0] IsLeaderData:[0] Department:[3] DepartmentData:[3] Order:[0] OrderData:[0] CreatedAt:2025-07-15 17:12:47.938 +0800 CST UpdatedAt:2025-07-22 18:09:03.336 +0800 CST DeletedAt:{Time:0001-01-01 00:00:00 +0000 UTC Valid:false} Departments:[{ID:5 WechatID:3 Name:设计研发部 NameEn: ParentID:1 Sort:99999000 Status:1 CreatedAt:2025-07-08 17:01:18.767 +0800 CST UpdatedAt:2025-07-15 17:38:02.663 +0800 CST DeletedAt:{Time:0001-01-01 00:00:00 +0000 UTC Valid:false} Children:[]}]}\n","time":"2025-07-23 08:54:21"}
{"body_size":662,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":10800500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getUserInfo","status_code":200,"time":"2025-07-23 08:54:21","timestamp":"2025-07-23 08:54:21","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":1875,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":16759600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getCourseList?page=1\u0026total=0\u0026pageSize=10\u0026pages=0","status_code":200,"time":"2025-07-23 08:54:21","timestamp":"2025-07-23 08:54:21","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户 41 的试卷列表","time":"2025-07-23 08:54:22"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"用户 41 的考核记录数量: 1\n","time":"2025-07-23 08:54:22"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"返回试卷列表 - 考试试卷: 1, 刷题试卷: 3","time":"2025-07-23 08:54:22"}
{"body_size":1673,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":83056100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamList","status_code":200,"time":"2025-07-23 08:54:22","timestamp":"2025-07-23 08:54:22","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":-1,"client_ip":"***************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/startExam","status_code":404,"time":"2025-07-23 08:54:44","timestamp":"2025-07-23 08:54:44","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":948,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":27139900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getPracticeRecords","status_code":200,"time":"2025-07-23 08:55:28","timestamp":"2025-07-23 08:55:28","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":499,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":28673500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamRecords","status_code":200,"time":"2025-07-23 08:55:28","timestamp":"2025-07-23 08:55:28","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":103,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":34942300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getLearningStats","status_code":200,"time":"2025-07-23 08:55:28","timestamp":"2025-07-23 08:55:28","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"删除考核记录成功: %!s(uint=12)","time":"2025-07-23 08:55:28"}
{"body_size":33,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":269222100,"level":"info","method":"DELETE","msg":"HTTP Request","path":"/api/admin/assessment-records/12","status_code":200,"time":"2025-07-23 08:55:29","timestamp":"2025-07-23 08:55:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":43595100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-records?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:55:29","timestamp":"2025-07-23 08:55:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":11308,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":36202600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examResult/9","status_code":200,"time":"2025-07-23 08:55:35","timestamp":"2025-07-23 08:55:35","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户 41 的试卷列表","time":"2025-07-23 08:55:39"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"用户 41 的考核记录数量: 0\n","time":"2025-07-23 08:55:39"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"返回试卷列表 - 考试试卷: 0, 刷题试卷: 0","time":"2025-07-23 08:55:39"}
{"body_size":75,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":40903700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamList","status_code":200,"time":"2025-07-23 08:55:39","timestamp":"2025-07-23 08:55:39","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"***************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamList","status_code":401,"time":"2025-07-23 08:55:40","timestamp":"2025-07-23 08:55:40","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":499,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":87099000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamRecords","status_code":200,"time":"2025-07-23 08:55:42","timestamp":"2025-07-23 08:55:42","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":948,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":79401300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getPracticeRecords","status_code":200,"time":"2025-07-23 08:55:43","timestamp":"2025-07-23 08:55:43","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":103,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":225661500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getLearningStats","status_code":200,"time":"2025-07-23 08:55:43","timestamp":"2025-07-23 08:55:43","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":11308,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":88567100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examResult/9","status_code":200,"time":"2025-07-23 08:55:48","timestamp":"2025-07-23 08:55:48","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户 41 的试卷列表","time":"2025-07-23 08:55:50"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"用户 41 的考核记录数量: 0\n","time":"2025-07-23 08:55:50"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"返回试卷列表 - 考试试卷: 0, 刷题试卷: 0","time":"2025-07-23 08:55:50"}
{"body_size":75,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":50020300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamList","status_code":200,"time":"2025-07-23 08:55:50","timestamp":"2025-07-23 08:55:50","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":12104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":23021700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:55:55","timestamp":"2025-07-23 08:55:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":2104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":12117300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026department_id=3\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:56:02","timestamp":"2025-07-23 08:56:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":2104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":6044500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026department_id=3\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:56:02","timestamp":"2025-07-23 08:56:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"管理员登录成功: admin","time":"2025-07-23 08:56:19"}
{"body_size":620,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":10876700,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-23 08:56:19","timestamp":"2025-07-23 08:56:19","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":717,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":95047300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-23 08:56:19","timestamp":"2025-07-23 08:56:19","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":730,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":47658200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/departments/tree","status_code":200,"time":"2025-07-23 08:56:21","timestamp":"2025-07-23 08:56:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":6513,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":39836000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:56:21","timestamp":"2025-07-23 08:56:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":35959400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-records?page=1\u0026page_size=10","status_code":200,"time":"2025-07-23 08:56:25","timestamp":"2025-07-23 08:56:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":730,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":55844000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/departments/tree","status_code":200,"time":"2025-07-23 08:56:25","timestamp":"2025-07-23 08:56:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"查询考核视频列表成功: [{10 07-21测试补考 测试 1 4 1 2025-07-21 19:02:33.043 +0800 CST 2025-07-21 19:02:33.043 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f8480 0xc00061e600 [] [] []} {9 07-21测试 测试 1 4 1 2025-07-21 19:02:32.79 +0800 CST 2025-07-21 19:02:32.79 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f8480 0xc00061e600 [{25 9 9 1 2025-07-21 19:02:32.823 +0800 CST 2025-07-21 19:02:32.823 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6800} {26 9 8 2 2025-07-21 19:02:32.825 +0800 CST 2025-07-21 19:02:32.825 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6700} {27 9 6 3 2025-07-21 19:02:32.856 +0800 CST 2025-07-21 19:02:32.856 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6600} {28 9 4 4 2025-07-21 19:02:32.871 +0800 CST 2025-07-21 19:02:32.871 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6400}] [] []} {6 补考 补考 1 3 1 2025-07-21 18:08:59.892 +0800 CST 2025-07-21 18:08:59.892 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f8480 0xc00061e540 [{17 6 6 1 2025-07-21 18:08:59.912 +0800 CST 2025-07-21 18:08:59.912 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6600} {18 6 8 2 2025-07-21 18:08:59.913 +0800 CST 2025-07-21 18:08:59.913 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6700} {19 6 9 3 2025-07-21 18:08:59.914 +0800 CST 2025-07-21 18:08:59.914 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6800} {20 6 4 4 2025-07-21 18:08:59.915 +0800 CST 2025-07-21 18:08:59.915 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6400}] [] []} {5 测试7-16 测试数据 1 3 1 2025-07-16 10:21:04.553 +0800 CST 2025-07-17 14:57:03.685 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f8480 0xc00061e540 [{15 5 6 1 2025-07-17 14:57:03.797 +0800 CST 2025-07-17 14:57:03.797 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6600} {16 5 4 2 2025-07-17 14:57:03.826 +0800 CST 2025-07-17 14:57:03.826 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6400}] [] []} {4 测试考核 测试数据 1 10 1 2025-07-15 18:01:34.556 +0800 CST 2025-07-15 18:01:34.556 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f8480 0xc00061e6c0 [{7 4 4 1 2025-07-15 18:01:34.561 +0800 CST 2025-07-15 18:01:34.561 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6400} {8 4 6 2 2025-07-15 18:01:34.563 +0800 CST 2025-07-15 18:01:34.563 +0800 CST {0001-01-01 00:00:00 +0000 UTC false} 0xc0005f6600}] [] []}]","time":"2025-07-23 08:56:25"}
{"body_size":12510,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":135754000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=100","status_code":200,"time":"2025-07-23 08:56:25","timestamp":"2025-07-23 08:56:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":12814,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":93158100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees?page=1\u0026page_size=20\u0026name=","status_code":200,"time":"2025-07-23 08:56:25","timestamp":"2025-07-23 08:56:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":12104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":10441900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:56:41","timestamp":"2025-07-23 08:56:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":2104,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":6935600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/employees/search?keyword=\u0026department_id=3\u0026page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:56:44","timestamp":"2025-07-23 08:56:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":3633,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":68987900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos/search?page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:56:47","timestamp":"2025-07-23 08:56:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":3633,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":17273500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos/search?page=1\u0026page_size=20","status_code":200,"time":"2025-07-23 08:56:47","timestamp":"2025-07-23 08:56:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"批量创建考核记录成功: 1条","time":"2025-07-23 08:56:50"}
{"body_size":52,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":7786800,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/assessment-records","status_code":200,"time":"2025-07-23 08:56:50","timestamp":"2025-07-23 08:56:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1758,"client_ip":"::1","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":19197300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-records?page=1\u0026page_size=10\u0026department_id=3","status_code":200,"time":"2025-07-23 08:56:50","timestamp":"2025-07-23 08:56:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"发送企业微信消息成功: ChenZhaoYu","time":"2025-07-23 08:56:51"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"考核通知发送成功，通知用户数: 1","time":"2025-07-23 08:56:51"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户成功\u0026{ID:41 UserID:ChenZhaoYu Name:陈兆雨 Position: Mobile:13083409461 Gender:1 Email: Avatar:https://wework.qpic.cn/wwpic3az/218437_3f4SXnA4RhyZPcl_1707498418/0 Telephone: Alias: Address: OpenUserID: MainDeptID:3 Status:1 IsLeaderInDept:[0] IsLeaderData:[0] Department:[3] DepartmentData:[3] Order:[0] OrderData:[0] CreatedAt:2025-07-15 17:12:47.938 +0800 CST UpdatedAt:2025-07-22 18:09:03.336 +0800 CST DeletedAt:{Time:0001-01-01 00:00:00 +0000 UTC Valid:false} Departments:[{ID:5 WechatID:3 Name:设计研发部 NameEn: ParentID:1 Sort:99999000 Status:1 CreatedAt:2025-07-08 17:01:18.767 +0800 CST UpdatedAt:2025-07-15 17:38:02.663 +0800 CST DeletedAt:{Time:0001-01-01 00:00:00 +0000 UTC Valid:false} Children:[]}]}\n","time":"2025-07-23 08:56:58"}
{"body_size":662,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":30964300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getUserInfo","status_code":200,"time":"2025-07-23 08:56:58","timestamp":"2025-07-23 08:56:58","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":1878,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":130993400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getCourseList?page=1\u0026total=0\u0026pageSize=10\u0026pages=0","status_code":200,"time":"2025-07-23 08:57:01","timestamp":"2025-07-23 08:57:01","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"获取用户 41 的试卷列表","time":"2025-07-23 08:57:02"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"用户 41 的考核记录数量: 1\n","time":"2025-07-23 08:57:02"}
{"file":"G:/code/go/hr_course/admin/logger/logger.go:67","func":"ai_select_admin/logger.Infof","level":"info","msg":"返回试卷列表 - 考试试卷: 1, 刷题试卷: 3","time":"2025-07-23 08:57:02"}
{"body_size":1676,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":42997700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamList","status_code":200,"time":"2025-07-23 08:57:02","timestamp":"2025-07-23 08:57:02","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":101,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":33826200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/h5/startExam","status_code":200,"time":"2025-07-23 08:57:05","timestamp":"2025-07-23 08:57:05","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":10385,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":21881400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examDetail/10","status_code":200,"time":"2025-07-23 08:57:05","timestamp":"2025-07-23 08:57:05","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":79,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":236264200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/h5/submitExam","status_code":200,"time":"2025-07-23 08:57:24","timestamp":"2025-07-23 08:57:24","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":13038,"client_ip":"***********","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":9340900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examResult/10","status_code":200,"time":"2025-07-23 08:57:25","timestamp":"2025-07-23 08:57:25","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"**************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examDetail/8","status_code":401,"time":"2025-07-23 09:01:42","timestamp":"2025-07-23 09:01:42","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":-1,"client_ip":"**************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/submitExam","status_code":404,"time":"2025-07-23 09:01:51","timestamp":"2025-07-23 09:01:51","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"*************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examDetail/9","status_code":401,"time":"2025-07-23 09:02:04","timestamp":"2025-07-23 09:02:04","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"**************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getExamRecords","status_code":401,"time":"2025-07-23 09:02:07","timestamp":"2025-07-23 09:02:07","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"***************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":922200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getPracticeRecords","status_code":401,"time":"2025-07-23 09:02:23","timestamp":"2025-07-23 09:02:23","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"***************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":786700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getCourseList?page=1\u0026total=0\u0026pageSize=10\u0026pages=0","status_code":401,"time":"2025-07-23 09:02:53","timestamp":"2025-07-23 09:02:53","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"***************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getUserInfo","status_code":401,"time":"2025-07-23 09:03:01","timestamp":"2025-07-23 09:03:01","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"**************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getCourseList?page=1\u0026total=0\u0026pageSize=10\u0026pages=0","status_code":401,"time":"2025-07-23 09:03:02","timestamp":"2025-07-23 09:03:02","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"**************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/getLearningStats","status_code":401,"time":"2025-07-23 09:03:07","timestamp":"2025-07-23 09:03:07","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"**************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examDetail/10","status_code":401,"time":"2025-07-23 09:04:05","timestamp":"2025-07-23 09:04:05","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
{"body_size":58,"client_ip":"**************","error":"","file":"G:/code/go/hr_course/admin/middleware/logger.go:25","func":"ai_select_admin/middleware.LoggerMiddleware.func1","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/h5/examResult/10","status_code":401,"time":"2025-07-23 09:05:08","timestamp":"2025-07-23 09:05:08","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"}
