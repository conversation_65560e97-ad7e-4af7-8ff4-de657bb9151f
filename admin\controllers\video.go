package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type VideoController struct{}

// ListVideos 获取视频列表
func (vc *VideoController) ListVideos(c *gin.Context) {
	var req models.VideoListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.Video{}).Preload("Category")

	// 按标题搜索
	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 按分类筛选
	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("查询视频总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var videos []models.Video
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&videos).Error; err != nil {
		logger.Errorf("查询视频列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var videoResponses []*models.VideoResponse
	for _, video := range videos {
		videoResponses = append(videoResponses, video.ToResponse())
	}

	// 计算分页信息
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	response := map[string]interface{}{
		"data": videoResponses,
		"page": map[string]interface{}{
			"current": req.Page,
			"size":    req.PageSize,
			"total":   total,
			"pages":   totalPages,
		},
	}

	utils.Success(c, response)
}

// GetVideo 获取视频详情
func (vc *VideoController) GetVideo(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的视频ID")
		return
	}

	var video models.Video
	if err := database.DB.Preload("Category").First(&video, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "视频不存在")
			return
		}
		logger.Errorf("查询视频详情失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	utils.Success(c, video.ToResponse())
}

// CreateVideo 创建视频
func (vc *VideoController) CreateVideo(c *gin.Context) {
	var req models.VideoCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 检查 FileId 是否已存在
	var existingVideo models.Video
	if err := database.DB.Where("file_id = ?", req.FileId).First(&existingVideo).Error; err == nil {
		utils.BadRequest(c, "该视频已存在")
		return
	}

	// 如果指定了分类，检查分类是否存在
	if req.CategoryID > 0 {
		var category models.Category
		if err := database.DB.First(&category, req.CategoryID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "指定的分类不存在")
				return
			}
			logger.Errorf("查询分类失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	// 创建视频记录
	video := models.Video{
		Title:       req.Title,
		Description: req.Description,
		FileName:    req.FileName,
		FileId:      req.FileId,
		PlayUrl:     req.PlayUrl,
		CoverUrl:    req.CoverUrl,
		Duration:    req.Duration,
		FileSize:    req.FileSize,
		Width:       req.Width,
		Height:      req.Height,
		Format:      req.Format,
		Status:      2, // 默认为转码中
		CategoryID:  req.CategoryID,
	}

	if err := database.DB.Create(&video).Error; err != nil {
		logger.Errorf("创建视频失败: %v", err)
		utils.InternalServerError(c, "创建视频失败")
		return
	}

	// 重新查询以获取关联数据
	if err := database.DB.Preload("Category").First(&video, video.ID).Error; err != nil {
		logger.Errorf("查询新创建的视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("创建视频成功: %s", video.Title)
	utils.SuccessWithMsg(c, "创建成功", video.ToResponse())
}

// UpdateVideo 更新视频
func (vc *VideoController) UpdateVideo(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的视频ID")
		return
	}

	var req models.VideoUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查询视频是否存在
	var video models.Video
	if err := database.DB.First(&video, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "视频不存在")
			return
		}
		logger.Errorf("查询视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 如果指定了分类，检查分类是否存在
	if req.CategoryID > 0 {
		var category models.Category
		if err := database.DB.First(&category, req.CategoryID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "指定的分类不存在")
				return
			}
			logger.Errorf("查询分类失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	// 更新视频信息
	video.Title = req.Title
	video.Description = req.Description
	video.PlayUrl = req.PlayUrl
	video.CoverUrl = req.CoverUrl
	video.Duration = req.Duration
	video.FileSize = req.FileSize
	video.Width = req.Width
	video.Height = req.Height
	video.Format = req.Format
	video.Status = req.Status
	video.CategoryID = req.CategoryID

	if err := database.DB.Save(&video).Error; err != nil {
		logger.Errorf("更新视频失败: %v", err)
		utils.InternalServerError(c, "更新视频失败")
		return
	}

	// 重新查询以获取关联数据
	if err := database.DB.Preload("Category").First(&video, video.ID).Error; err != nil {
		logger.Errorf("查询更新后的视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("更新视频成功: %s", video.Title)
	utils.SuccessWithMsg(c, "更新成功", video.ToResponse())
}

// DeleteVideo 删除视频
func (vc *VideoController) DeleteVideo(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的视频ID")
		return
	}

	// 查询视频是否存在
	var video models.Video
	if err := database.DB.First(&video, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "视频不存在")
			return
		}
		logger.Errorf("查询视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 软删除视频
	if err := database.DB.Delete(&video).Error; err != nil {
		logger.Errorf("删除视频失败: %v", err)
		utils.InternalServerError(c, "删除视频失败")
		return
	}

	logger.Infof("删除视频成功: %s", video.Title)
	utils.SuccessWithMsg(c, "删除成功", nil)
}
