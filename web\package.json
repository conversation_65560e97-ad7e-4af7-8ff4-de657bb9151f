{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "cos-js-sdk-v5": "^1.8.3", "element-plus": "^2.10.2", "moment": "^2.30.1", "pinia": "^3.0.3", "vod-js-sdk-v6": "^1.7.1-beta.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}