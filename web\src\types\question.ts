import type { QuestionCategory } from './questionCategory'

// 题目选项
export interface QuestionOption {
  label: string
  content: string
}

// 题目
export interface Question {
  id: number
  title: string
  content: string
  type: number
  options: QuestionOption[]
  correct_answer: string
  difficulty: number
  category_id: number
  category?: QuestionCategory
  status: number
  created_at: string
  updated_at: string
  score?: number
}

// 题目列表请求参数
export interface QuestionListRequest {
  page?: number
  page_size?: number
  title?: string
  category_id?: number
  type?: number
  difficulty?: number
  status?: number
}

// 创建题目请求参数
export interface QuestionCreateRequest {
  title: string
  content: string
  type: number
  options: QuestionOption[]
  correct_answer: string
  difficulty: number
  category_id: number
  status?: number
}

// 更新题目请求参数
export interface QuestionUpdateRequest {
  title: string
  content: string
  type: number
  options: QuestionOption[]
  correct_answer: string
  difficulty: number
  category_id: number
  status?: number
}

// 题目类型选项
export const QUESTION_TYPES = [
  { label: '单选题', value: 1 },
  { label: '多选题', value: 2 },
  { label: '判断题', value: 3 }
]

// 难度等级选项
export const DIFFICULTY_LEVELS = [
  { label: '简单', value: 1 },
  { label: '中等', value: 2 },
  { label: '困难', value: 3 }
]
