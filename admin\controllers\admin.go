package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AdminController struct{}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Token    string       `json:"token"`
	UserInfo models.Admin `json:"user_info"`
}

// Login 管理员登录
func (ac *AdminController) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查找管理员
	var admin models.Admin
	if err := database.DB.Where("username = ? AND status = 1", req.Username).First(&admin).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.BadRequest(c, "用户名或密码错误")
		} else {
			logger.Errorf("查询管理员失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 验证密码
	if !admin.CheckPassword(req.Password) {
		utils.BadRequest(c, "用户名或密码错误")
		return
	}

	// 生成token
	token, err := utils.GenerateToken(admin.ID, admin.Username, "admin")
	if err != nil {
		logger.Errorf("生成token失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 更新最后登录时间
	now := time.Now()
	admin.LastLogin = &now
	database.DB.Save(&admin)

	// 返回响应
	utils.Success(c, LoginResponse{
		Token:    token,
		UserInfo: admin,
	})

	logger.Infof("管理员登录成功: %s", admin.Username)
}

// GetProfile 获取管理员信息
func (ac *AdminController) GetProfile(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var admin models.Admin
	if err := database.DB.First(&admin, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "管理员不存在")
		} else {
			logger.Errorf("查询管理员失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, admin)
}

// ListAdmins 获取管理员列表
func (ac *AdminController) ListAdmins(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	keyword := c.Query("keyword")

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	offset := (page - 1) * size

	// 构建查询
	query := database.DB.Model(&models.Admin{})
	if keyword != "" {
		query = query.Where("username LIKE ? OR email LIKE ? OR nickname LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	query.Count(&total)

	// 获取数据
	var admins []models.Admin
	if err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&admins).Error; err != nil {
		logger.Errorf("查询管理员列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 计算总页数
	pages := int(total) / size
	if int(total)%size > 0 {
		pages++
	}

	pageInfo := utils.PageInfo{
		Current: page,
		Size:    size,
		Total:   total,
		Pages:   pages,
	}

	utils.PageSuccess(c, admins, pageInfo)
}

// CreateAdminRequest 创建管理员请求结构
type CreateAdminRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required,min=6"`
	Email    string `json:"email" binding:"email"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Status   int    `json:"status"`
}

// CreateAdmin 创建管理员
func (ac *AdminController) CreateAdmin(c *gin.Context) {
	var req CreateAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 检查用户名是否已存在
	var count int64
	database.DB.Model(&models.Admin{}).Where("username = ?", req.Username).Count(&count)
	if count > 0 {
		utils.BadRequest(c, "用户名已存在")
		return
	}

	// 创建管理员
	admin := models.Admin{
		Username: req.Username,
		Password: req.Password, // 会在BeforeCreate中自动加密
		Email:    req.Email,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		Status:   req.Status,
	}

	if admin.Status == 0 {
		admin.Status = 1 // 默认启用
	}

	if err := database.DB.Create(&admin).Error; err != nil {
		logger.Errorf("创建管理员失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	utils.SuccessWithMsg(c, "创建成功", admin)
	logger.Infof("创建管理员成功: %s", admin.Username)
}

// UpdateAdminRequest 更新管理员请求结构
type UpdateAdminRequest struct {
	Email    string `json:"email" binding:"email"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Status   int    `json:"status"`
	Password string `json:"password" binding:"omitempty,min=6"` // 可选的密码字段
}

// UpdateAdmin 更新管理员
func (ac *AdminController) UpdateAdmin(c *gin.Context) {
	id := c.Param("id")
	adminID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的管理员ID")
		return
	}

	var req UpdateAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查找管理员
	var admin models.Admin
	if err := database.DB.First(&admin, adminID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "管理员不存在")
		} else {
			logger.Errorf("查询管理员失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 更新字段
	admin.Email = req.Email
	admin.Nickname = req.Nickname
	admin.Avatar = req.Avatar
	admin.Status = req.Status

	// 如果提供了新密码，则更新密码
	if req.Password != "" {
		// 生成新的盐值
		admin.Salt = models.GenerateSalt()
		// 加密新密码
		admin.Password = models.HashPassword(req.Password, admin.Salt)
	}

	if err := database.DB.Save(&admin).Error; err != nil {
		logger.Errorf("更新管理员失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	utils.SuccessWithMsg(c, "更新成功", admin)
	logger.Infof("更新管理员成功: %s", admin.Username)
}

// DeleteAdmin 删除管理员
func (ac *AdminController) DeleteAdmin(c *gin.Context) {
	id := c.Param("id")
	adminID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的管理员ID")
		return
	}

	// 不能删除自己
	currentUserID, _ := c.Get("user_id")
	if uint(adminID) == currentUserID.(uint) {
		utils.BadRequest(c, "不能删除自己")
		return
	}

	// 软删除管理员
	if err := database.DB.Delete(&models.Admin{}, adminID).Error; err != nil {
		logger.Errorf("删除管理员失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	utils.SuccessWithMsg(c, "删除成功", nil)
	logger.Infof("删除管理员成功: ID=%d", adminID)
}
