import request from '@/utils/request'
import type { 
  Category, 
  CategoryCreateRequest, 
  CategoryUpdateRequest,
  CategoryListRequest 
} from '@/types/category'
import type { ApiResponse, PageResponse,PageInfoResponse } from '@/types/common'

// 获取分类列表
export const getCategoryList = (params: CategoryListRequest): Promise<PageInfoResponse<Category[]>> => {
  return request.get('/admin/categories', { params })
}

// 获取分类树
export const getCategoryTree = (): Promise<ApiResponse<Category[]>> => {
  return request.get('/admin/categories/tree')
}

// 获取分类详情
export const getCategory = (id: number): Promise<ApiResponse<Category>> => {
  return request.get(`/admin/categories/${id}`)
}

// 创建分类
export const createCategory = (data: CategoryCreateRequest): Promise<ApiResponse<Category>> => {
  return request.post('/admin/categories', data)
}

// 更新分类
export const updateCategory = (id: number, data: CategoryUpdateRequest): Promise<ApiResponse<Category>> => {
  return request.put(`/admin/categories/${id}`, data)
}

// 删除分类
export const deleteCategory = (id: number): Promise<ApiResponse<null>> => {
  return request.delete(`/admin/categories/${id}`)
}
