import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import {showFailToast, showLoadingToast,closeToast} from 'vant';


const request = axios.create({
  baseURL: '/api/h5',
  timeout: 100000,
})

// 请求拦截器
request.interceptors.request.use(
  (config: any) => {

    if(config?.showLoading !==false){
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
      })
  }
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    closeToast()
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    closeToast()
    const { code, msg } = response.data
    
    if (code === 200) {
      return response.data
    } else {
      showFailToast(msg || '请求失败')
      return Promise.reject(new Error(msg || '请求失败'))
    }
  },
  (error) => {
    closeToast()
    if (error.response) {
      const { status, data } = error.response
      if(status ==401){
      const authStore = useAuthStore()
       authStore.clearToken()
        //重新登录
      authStore.login()
      }else if (status === 403) {
        showFailToast('权限不足')
      } else if (status === 404) {
        showFailToast('请求的资源不存在')
      } else if (status >= 500) {
        showFailToast('服务器错误')
      } else {
        showFailToast(data?.msg || '请求失败')
      }
    } else if (error.request) {
      showFailToast('网络错误，请检查网络连接')
    } else {
      showFailToast('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default request
