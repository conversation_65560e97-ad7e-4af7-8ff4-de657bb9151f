package models

import (
	"time"
)

// VideoProgress 视频观看进度模型
type VideoProgress struct {
	ID                 uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	EmployeeID         uint      `json:"employee_id" gorm:"type:int unsigned;not null;comment:员工ID"`
	AssessmentRecordID uint      `json:"assessment_record_id" gorm:"type:int unsigned;not null;comment:考核记录ID"`
	VideoID            uint      `json:"video_id" gorm:"type:int unsigned;not null;comment:视频ID"`
	Progress           int       `json:"progress" gorm:"type:int;default:0;comment:观看进度百分比(0-100)"`
	Duration           int       `json:"duration" gorm:"type:int;default:0;comment:观看时长(秒)"`
	CreatedAt          time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt          time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// 关联关系
	Employee         *Employee         `json:"employee,omitempty" gorm:"foreignKey:EmployeeID"`
	AssessmentRecord *AssessmentRecord `json:"assessment_record,omitempty" gorm:"foreignKey:AssessmentRecordID"`
	Video            *Video            `json:"video,omitempty" gorm:"foreignKey:VideoID"`
}
