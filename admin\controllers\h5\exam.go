package h5

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ExamController struct{}

// StartExam 开始考试
func (ec *ExamController) StartExam(c *gin.Context) {
	uid, _ := c.Get("user_id")
	var req models.ExamStartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证试卷是否存在且可用
	var exam models.Exam
	err := database.DB.Where("id = ? AND status = 1 AND deleted_at IS NULL", req.ExamID).First(&exam).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			utils.NotFound(c, "试卷不存在或已禁用")
		} else {
			logger.Errorf("查询试卷失败: %v", err)
			utils.InternalServerError(c, "查询试卷失败")
		}
		return
	}

	// 检查是否已有进行中的考试记录
	var existingRecord models.ExamRecord
	err = database.DB.Where("employee_id = ? AND exam_id = ? AND status IN (?, ?) AND deleted_at IS NULL",
		uid, req.ExamID, models.ExamRecordStatusNotStarted, models.ExamRecordStatusInProgress).
		First(&existingRecord).Error

	if err == nil {
		// 已有进行中的考试，返回现有记录
		utils.Success(c, gin.H{
			"exam_record_id": existingRecord.ID,
			"status":         "existing",
			"message":        "已有进行中的考试",
		})
		return
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Errorf("查询考试记录失败: %v", err)
		utils.InternalServerError(c, "查询考试记录失败")
		return
	}

	// 如果是考试试卷，检查是否已经考过
	// if exam.Type == 1 {
	// 	var completedRecord models.ExamRecord
	// 	err = database.DB.Where("employee_id = ? AND exam_id = ? AND status = ? AND deleted_at IS NULL",
	// 		uid, req.ExamID, models.ExamRecordStatusCompleted).
	// 		First(&completedRecord).Error

	// 	if err == nil {
	// 		utils.BadRequest(c, "该考试已完成，不能重复考试")
	// 		return
	// 	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
	// 		logger.Errorf("查询已完成考试记录失败: %v", err)
	// 		utils.InternalServerError(c, "查询考试记录失败")
	// 		return
	// 	}
	// }

	// 创建新的考试记录
	now := time.Now()
	examRecord := models.ExamRecord{
		EmployeeID:         uid.(uint),
		ExamID:             req.ExamID,
		AssessmentRecordID: req.AssessmentRecordID,
		Status:             models.ExamRecordStatusInProgress,
		StartTime:          &now,
		TotalScore:         exam.TotalScore,
		PassScore:          exam.PassScore,
	}

	// 获取试卷题目数量
	var questionCount int64
	database.DB.Model(&models.ExamQuestion{}).Where("exam_id = ? AND deleted_at IS NULL", req.ExamID).Count(&questionCount)
	examRecord.TotalCount = int(questionCount)

	err = database.DB.Create(&examRecord).Error
	if err != nil {
		logger.Errorf("创建考试记录失败: %v", err)
		utils.InternalServerError(c, "创建考试记录失败")
		return
	}

	utils.Success(c, gin.H{
		"exam_record_id": examRecord.ID,
		"status":         "started",
		"message":        "考试开始",
	})
}

// GetExamDetail 获取考试详情（用于答题页面）
func (ec *ExamController) GetExamDetail(c *gin.Context) {
	uid, _ := c.Get("user_id")
	examRecordIDStr := c.Param("id")
	examRecordID, err := strconv.ParseUint(examRecordIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考试记录ID")
		return
	}

	// 查询考试记录
	var examRecord models.ExamRecord
	err = database.DB.Preload("Exam").Preload("Employee").
		Where("id = ? AND employee_id = ? AND deleted_at IS NULL", uint(examRecordID), uid).
		First(&examRecord).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			utils.NotFound(c, "考试记录不存在或无权限访问")
		} else {
			logger.Errorf("查询考试记录失败: %v", err)
			utils.InternalServerError(c, "查询考试记录失败")
		}
		return
	}

	// 检查考试状态
	if examRecord.Status == models.ExamRecordStatusCompleted ||
		examRecord.Status == models.ExamRecordStatusTimeout ||
		examRecord.Status == models.ExamRecordStatusCancelled {
		utils.BadRequest(c, "考试已结束")
		return
	}

	// 检查是否超时
	if examRecord.StartTime != nil {
		elapsed := time.Since(*examRecord.StartTime)
		if elapsed.Minutes() > float64(examRecord.Exam.Duration) {
			// 超时，自动提交
			ec.autoSubmitExam(examRecord.ID, "timeout")
			utils.BadRequest(c, "考试已超时")
			return
		}
	}

	// 获取试卷题目
	var examQuestions []models.ExamQuestion
	err = database.DB.Preload("Question").
		Where("exam_id = ? AND deleted_at IS NULL", examRecord.ExamID).
		Order("sort ASC").
		Find(&examQuestions).Error

	if err != nil {
		logger.Errorf("查询试卷题目失败: %v", err)
		utils.InternalServerError(c, "查询试卷题目失败")
		return
	}

	// 获取用户已答题目
	var answers []models.ExamAnswer
	database.DB.Where("exam_record_id = ? AND deleted_at IS NULL", examRecord.ID).Find(&answers)

	// 构建答案映射
	answerMap := make(map[uint]string)
	for _, answer := range answers {
		answerMap[answer.QuestionID] = answer.Answer
	}

	// 构建题目详情
	var questionDetails []models.ExamQuestionDetail
	for _, eq := range examQuestions {
		// 解析题目选项
		var options []models.QuestionOption
		if eq.Question.Options != "" {
			json.Unmarshal([]byte(eq.Question.Options), &options)
		}
		eq.Question.OptionsData = options

		userAnswer, isAnswered := answerMap[eq.QuestionID]

		questionDetail := models.ExamQuestionDetail{
			ID:         eq.ID,
			QuestionID: eq.QuestionID,
			Score:      eq.Score,
			Sort:       eq.Sort,
			Question: models.QuestionResponse{
				ID:         eq.Question.ID,
				Title:      eq.Question.Title,
				Content:    eq.Question.Content,
				Type:       eq.Question.Type,
				Options:    eq.Question.OptionsData,
				Difficulty: eq.Question.Difficulty,
				CategoryID: eq.Question.CategoryID,
				Status:     eq.Question.Status,
				CreatedAt:  eq.Question.CreatedAt,
				UpdatedAt:  eq.Question.UpdatedAt,
			},
			UserAnswer: userAnswer,
			IsAnswered: isAnswered,
		}
		questionDetails = append(questionDetails, questionDetail)
	}

	// 计算剩余时间
	timeLeft := 0
	if examRecord.StartTime != nil {
		elapsed := time.Since(*examRecord.StartTime)
		totalDuration := time.Duration(examRecord.Exam.Duration) * time.Minute
		remaining := totalDuration - elapsed
		if remaining > 0 {
			timeLeft = int(remaining.Seconds())
		}
	}

	response := models.ExamDetailResponse{
		ExamRecord: models.ExamRecordResponse{
			ID:                 examRecord.ID,
			EmployeeID:         examRecord.EmployeeID,
			ExamID:             examRecord.ExamID,
			Title:              examRecord.Exam.Title,
			AssessmentRecordID: examRecord.AssessmentRecordID,
			Status:             examRecord.Status,
			StatusText:         examRecord.GetStatusText(),
			StartTime:          examRecord.StartTime,
			EndTime:            examRecord.EndTime,
			SubmitTime:         examRecord.SubmitTime,
			Duration:           examRecord.Duration,
			Score:              examRecord.Score,
			TotalScore:         examRecord.TotalScore,
			PassScore:          examRecord.PassScore,
			IsPassed:           examRecord.IsPassed,
			CorrectCount:       examRecord.CorrectCount,
			TotalCount:         examRecord.TotalCount,
			CreatedAt:          examRecord.CreatedAt,
			UpdatedAt:          examRecord.UpdatedAt,
		},
		Questions: questionDetails,
		TimeLeft:  timeLeft,
	}

	utils.Success(c, response)
}

// SaveAnswer 保存答案（单题保存）
func (ec *ExamController) SaveAnswer(c *gin.Context) {
	uid, _ := c.Get("user_id")
	var req struct {
		ExamRecordID uint   `json:"exam_record_id" binding:"required"`
		QuestionID   uint   `json:"question_id" binding:"required"`
		Answer       string `json:"answer"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证考试记录
	var examRecord models.ExamRecord
	err := database.DB.Where("id = ? AND employee_id = ? AND status = ? AND deleted_at IS NULL",
		req.ExamRecordID, uid, models.ExamRecordStatusInProgress).
		First(&examRecord).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			utils.NotFound(c, "考试记录不存在或已结束")
		} else {
			logger.Errorf("查询考试记录失败: %v", err)
			utils.InternalServerError(c, "查询考试记录失败")
		}
		return
	}

	// 保存或更新答案
	now := time.Now()
	var answer models.ExamAnswer
	err = database.DB.Where("exam_record_id = ? AND question_id = ? AND deleted_at IS NULL",
		req.ExamRecordID, req.QuestionID).First(&answer).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Errorf("查询答案记录失败: %v", err)
		utils.InternalServerError(c, "查询答案记录失败")
		return
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新答案
		answer = models.ExamAnswer{
			ExamRecordID: req.ExamRecordID,
			QuestionID:   req.QuestionID,
			Answer:       req.Answer,
			AnswerTime:   &now,
		}
		err = database.DB.Create(&answer).Error
	} else {
		// 更新现有答案
		answer.Answer = req.Answer
		answer.AnswerTime = &now
		err = database.DB.Save(&answer).Error
	}

	if err != nil {
		logger.Errorf("保存答案失败: %v", err)
		utils.InternalServerError(c, "保存答案失败")
		return
	}

	utils.SuccessWithMsg(c, "答案保存成功", nil)
}

// SubmitExam 提交考试
func (ec *ExamController) SubmitExam(c *gin.Context) {
	uid, _ := c.Get("user_id")
	var req models.ExamSubmitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证考试记录
	var examRecord models.ExamRecord
	err := database.DB.Preload("Exam").
		Where("id = ? AND employee_id = ? AND status = ? AND deleted_at IS NULL",
			req.ExamRecordID, uid, models.ExamRecordStatusInProgress).
		First(&examRecord).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			utils.NotFound(c, "考试记录不存在或已结束")
		} else {
			logger.Errorf("查询考试记录失败: %v", err)
			utils.InternalServerError(c, "查询考试记录失败")
		}
		return
	}

	// 保存所有答案
	now := time.Now()
	for _, answerItem := range req.Answers {
		var answer models.ExamAnswer
		err = database.DB.Where("exam_record_id = ? AND question_id = ? AND deleted_at IS NULL",
			req.ExamRecordID, answerItem.QuestionID).First(&answer).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新答案
			answer = models.ExamAnswer{
				ExamRecordID: req.ExamRecordID,
				QuestionID:   answerItem.QuestionID,
				Answer:       answerItem.Answer,
				AnswerTime:   &now,
			}
			database.DB.Create(&answer)
		} else if err == nil {
			// 更新现有答案
			answer.Answer = answerItem.Answer
			answer.AnswerTime = &now
			database.DB.Save(&answer)
		}
	}

	// 自动批改
	err = ec.gradeExam(req.ExamRecordID)
	if err != nil {
		logger.Errorf("自动批改失败: %v", err)
		utils.InternalServerError(c, "自动批改失败")
		return
	}

	// 更新考试记录状态
	status := models.ExamRecordStatusCompleted
	if req.SubmitType == "timeout" {
		status = models.ExamRecordStatusTimeout
	}

	duration := 0
	if examRecord.StartTime != nil {
		duration = int(time.Since(*examRecord.StartTime).Seconds())
	}

	err = database.DB.Model(&examRecord).Updates(map[string]interface{}{
		"status":      status,
		"end_time":    &now,
		"submit_time": &now,
		"duration":    duration,
	}).Error

	if err != nil {
		logger.Errorf("更新考试记录失败: %v", err)
		utils.InternalServerError(c, "更新考试记录失败")
		return
	}

	// 如果是考核中的考试，且是考试试卷（非刷题试卷），更新考核记录
	if examRecord.AssessmentRecordID != nil && examRecord.Exam.Type == 1 {
		ec.updateAssessmentExamStatus(*examRecord.AssessmentRecordID, req.ExamRecordID)
	}

	utils.SuccessWithMsg(c, "考试提交成功", gin.H{
		"exam_record_id": req.ExamRecordID,
		"status":         status,
	})
}

// GetExamResult 获取考试结果
func (ec *ExamController) GetExamResult(c *gin.Context) {
	uid, _ := c.Get("user_id")
	examRecordIDStr := c.Param("id")
	examRecordID, err := strconv.ParseUint(examRecordIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考试记录ID")
		return
	}

	// 查询考试记录
	var examRecord models.ExamRecord
	err = database.DB.Preload("Exam").Preload("Answers.Question").
		Where("id = ? AND employee_id = ? AND deleted_at IS NULL", uint(examRecordID), uid).
		First(&examRecord).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			utils.NotFound(c, "考试记录不存在或无权限访问")
		} else {
			logger.Errorf("查询考试记录失败: %v", err)
			utils.InternalServerError(c, "查询考试记录失败")
		}
		return
	}

	// 检查考试是否已完成
	if examRecord.Status != models.ExamRecordStatusCompleted &&
		examRecord.Status != models.ExamRecordStatusTimeout {
		utils.BadRequest(c, "考试尚未完成")
		return
	}

	// 构建答案详情
	var answerDetails []models.ExamAnswerResponse
	for _, answer := range examRecord.Answers {
		// 解析题目选项
		var options []models.QuestionOption
		if answer.Question.Options != "" {
			json.Unmarshal([]byte(answer.Question.Options), &options)
		}

		answerDetail := models.ExamAnswerResponse{
			ID:           answer.ID,
			ExamRecordID: answer.ExamRecordID,
			QuestionID:   answer.QuestionID,
			Answer:       answer.Answer,
			IsCorrect:    answer.IsCorrect,
			Score:        answer.Score,
			AnswerTime:   answer.AnswerTime,
			Question: &models.QuestionResponse{
				ID:            answer.Question.ID,
				Title:         answer.Question.Title,
				Content:       answer.Question.Content,
				Type:          answer.Question.Type,
				Options:       options,
				CorrectAnswer: answer.Question.CorrectAnswer,
				Difficulty:    answer.Question.Difficulty,
				CategoryID:    answer.Question.CategoryID,
				Status:        answer.Question.Status,
				CreatedAt:     answer.Question.CreatedAt,
				UpdatedAt:     answer.Question.UpdatedAt,
			},
			CreatedAt: answer.CreatedAt,
			UpdatedAt: answer.UpdatedAt,
		}
		answerDetails = append(answerDetails, answerDetail)
	}

	response := models.ExamRecordResponse{
		ID:                 examRecord.ID,
		EmployeeID:         examRecord.EmployeeID,
		ExamID:             examRecord.ExamID,
		Title:              examRecord.Exam.Title,
		AssessmentRecordID: examRecord.AssessmentRecordID,
		Status:             examRecord.Status,
		StatusText:         examRecord.GetStatusText(),
		StartTime:          examRecord.StartTime,
		EndTime:            examRecord.EndTime,
		SubmitTime:         examRecord.SubmitTime,
		Duration:           examRecord.Duration,
		Score:              examRecord.Score,
		TotalScore:         examRecord.TotalScore,
		PassScore:          examRecord.PassScore,
		IsPassed:           examRecord.IsPassed,
		CorrectCount:       examRecord.CorrectCount,
		TotalCount:         examRecord.TotalCount,
		Answers:            answerDetails,
		CreatedAt:          examRecord.CreatedAt,
		UpdatedAt:          examRecord.UpdatedAt,
	}

	utils.Success(c, response)
}

// gradeExam 自动批改考试
func (ec *ExamController) gradeExam(examRecordID uint) error {
	// 获取考试记录和答案
	var examRecord models.ExamRecord
	err := database.DB.Preload("Answers").Where("id = ?", examRecordID).First(&examRecord).Error
	if err != nil {
		return err
	}

	// 获取试卷题目和正确答案
	var examQuestions []models.ExamQuestion
	err = database.DB.Preload("Question").Where("exam_id = ? AND deleted_at IS NULL", examRecord.ExamID).Find(&examQuestions).Error
	if err != nil {
		return err
	}

	// 构建题目答案映射
	questionMap := make(map[uint]models.ExamQuestion)
	for _, eq := range examQuestions {
		questionMap[eq.QuestionID] = eq
	}

	// 批改每道题
	totalScore := 0
	correctCount := 0
	for i := range examRecord.Answers {
		answer := &examRecord.Answers[i]
		if eq, exists := questionMap[answer.QuestionID]; exists {
			if answer.Answer == eq.Question.CorrectAnswer {
				answer.IsCorrect = true
				answer.Score = eq.Score
				correctCount++
			} else {
				answer.IsCorrect = false
				answer.Score = 0
			}
			totalScore += answer.Score

			// 更新答案记录
			database.DB.Save(answer)
		}
	}

	// 更新考试记录
	isPassed := totalScore >= examRecord.PassScore
	err = database.DB.Model(&examRecord).Updates(map[string]interface{}{
		"score":         totalScore,
		"correct_count": correctCount,
		"is_passed":     isPassed,
	}).Error

	return err
}

// updateAssessmentExamStatus 更新考核记录的考试状态
func (ec *ExamController) updateAssessmentExamStatus(assessmentRecordID uint, examRecordID uint) error {
	// 获取考试记录
	var examRecord models.ExamRecord
	err := database.DB.Where("id = ?", examRecordID).First(&examRecord).Error
	if err != nil {
		return err
	}

	// 更新考核记录的考试得分和状态
	updates := map[string]interface{}{
		"exam_score": examRecord.Score,
	}

	// 如果考试已完成，更新考核状态为已完成
	if examRecord.Status == models.ExamRecordStatusCompleted || examRecord.Status == models.ExamRecordStatusTimeout {
		updates["status"] = models.AssessmentRecordStatusCompleted
		updates["completed_at"] = time.Now()
	}

	err = database.DB.Model(&models.AssessmentRecord{}).
		Where("id = ?", assessmentRecordID).
		Updates(updates).Error

	return err
}

// autoSubmitExam 自动提交考试（超时或其他原因）
func (ec *ExamController) autoSubmitExam(examRecordID uint, submitType string) error {
	now := time.Now()
	status := models.ExamRecordStatusTimeout
	if submitType == "switch" {
		status = models.ExamRecordStatusCancelled
	}

	// 自动批改
	err := ec.gradeExam(examRecordID)
	if err != nil {
		logger.Errorf("自动批改失败: %v", err)
	}

	// 更新考试记录状态
	var examRecord models.ExamRecord
	err = database.DB.Preload("Exam").Where("id = ?", examRecordID).First(&examRecord).Error
	if err != nil {
		return err
	}

	duration := 0
	if examRecord.StartTime != nil {
		duration = int(time.Since(*examRecord.StartTime).Seconds())
	}

	err = database.DB.Model(&examRecord).Updates(map[string]interface{}{
		"status":      status,
		"end_time":    &now,
		"submit_time": &now,
		"duration":    duration,
	}).Error

	if err != nil {
		return err
	}

	// 如果是考核中的考试，且是考试试卷（非刷题试卷），更新考核记录
	if examRecord.AssessmentRecordID != nil && examRecord.Exam.Type == 1 {
		ec.updateAssessmentExamStatus(*examRecord.AssessmentRecordID, examRecordID)
	}

	return nil
}
