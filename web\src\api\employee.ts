import request from "@/utils/request";
import type { ApiResponse, PageInfo } from '@/types/common';

// 员工列表查询参数
export interface EmployeeListParams {
  page?: number;
  page_size?: number;
  name?: string;
  department_id?: number;
  status?: number;
}

// 员工搜索查询参数（用于下拉选择）
export interface EmployeeSearchParams {
  page?: number;
  page_size?: number;
  keyword?: string;
  department_id?: number;
}

// 员工信息
export interface Employee {
  id: number;
  user_id: string;
  name: string;
  position: string;
  mobile: string;
  gender: string;
  email: string;
  avatar: string;
  telephone: string;
  alias: string;
  address: string;
  open_userid: string;
  main_dept_id: number;
  status: number;
  status_text: string;
  is_leader_in_dept: number[];
  department: number[];
  order: number[];
  departments: Department[];
  created_at: string;
  updated_at: string;
}

// 部门信息
export interface Department {
  id: number;
  wechat_id: number;
  name: string;
  name_en: string;
  parent_id: number;
  sort: number;
  status: number;
  status_text: string;
  children?: Department[];
  created_at: string;
  updated_at: string;
}

interface PageResponse<T = any> extends ApiResponse<T> {
  page: PageInfo
}

// 获取员工列表
export function getEmployeeList(params: EmployeeListParams): Promise<PageResponse<Employee[]>> {
  return request({
    url: "/admin/employees",
    method: "get",
    params,
  });
}

// 获取员工详情
export function getEmployee(id: number) {
  return request({
    url: `/admin/employees/${id}`,
    method: "get",
  });
}

// 搜索员工（用于下拉选择）
export function searchEmployees(params: EmployeeSearchParams): Promise<PageResponse<Employee[]>> {
  return request({
    url: "/admin/employees/search",
    method: "get",
    params,
  });
}

// 同步员工数据
export function syncEmployees() {
  return request({
    url: "/admin/employees/sync",
    method: "post",
  });
}

// 同步所有数据（部门+员工）
export function syncAll() {
  return request({
    url: "/admin/employees/sync-all",
    method: "post",
  });
}

// 获取部门列表
export function getDepartmentList(params?: any) {
  return request({
    url: "/admin/departments",
    method: "get",
    params,
  });
}

// 获取部门树
export function getDepartmentTree() {
  return request({
    url: "/admin/departments/tree",
    method: "get",
  });
}

// 同步部门数据
export function syncDepartments() {
  return request({
    url: "/admin/departments/sync",
    method: "post",
  });
}
