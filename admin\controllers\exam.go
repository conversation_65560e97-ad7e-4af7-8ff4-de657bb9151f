package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ExamController struct{}

// ListExams 获取试卷列表
func (ec *ExamController) ListExams(c *gin.Context) {
	var req models.ExamListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.Exam{})

	// 按标题搜索
	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}

	// 按类型筛选
	if req.Type != nil {
		query = query.Where("type = ?", *req.Type)
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取试卷总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var exams []models.Exam
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("id DESC").Offset(offset).Limit(req.PageSize).Find(&exams).Error; err != nil {
		logger.Errorf("查询试卷列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var responses []*models.ExamResponse
	for _, exam := range exams {
		responses = append(responses, exam.ToResponse())
	}

	// 计算分页信息
	pages := int(math.Ceil(float64(total) / float64(req.PageSize)))
	pageInfo := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Total:   total,
		Pages:   pages,
	}

	utils.PageSuccess(c, responses, pageInfo)
}

// GetExam 获取试卷详情
func (ec *ExamController) GetExam(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的试卷ID")
		return
	}

	var exam models.Exam
	if err := database.DB.Preload("Questions").Preload("Questions.Question").Preload("Questions.Question.Category").First(&exam, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "试卷不存在")
		} else {
			logger.Errorf("查询试卷失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, exam.ToResponse())
}

// CreateExam 创建试卷
func (ec *ExamController) CreateExam(c *gin.Context) {
	var req models.ExamCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证及格分不能大于总分
	if req.PassScore > req.TotalScore {
		utils.BadRequest(c, "及格分不能大于总分")
		return
	}

	// 创建试卷
	exam := models.Exam{
		Title:       req.Title,
		Description: req.Description,
		Type:        req.Type,
		Duration:    req.Duration,
		TotalScore:  req.TotalScore,
		PassScore:   req.PassScore,
		Status:      req.Status,
	}

	// 设置默认状态
	if exam.Status == 0 {
		exam.Status = 1
	}

	if err := database.DB.Create(&exam).Error; err != nil {
		logger.Errorf("创建试卷失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("创建试卷成功: %s", exam.Title)
	utils.SuccessWithMsg(c, "创建成功", exam.ToResponse())
}

// UpdateExam 更新试卷
func (ec *ExamController) UpdateExam(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的试卷ID")
		return
	}

	var req models.ExamUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证及格分不能大于总分
	if req.PassScore > req.TotalScore {
		utils.BadRequest(c, "及格分不能大于总分")
		return
	}

	// 查找试卷
	var exam models.Exam
	if err := database.DB.First(&exam, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "试卷不存在")
		} else {
			logger.Errorf("查询试卷失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 更新试卷
	exam.Title = req.Title
	exam.Description = req.Description
	exam.Type = req.Type
	exam.Duration = req.Duration
	exam.TotalScore = req.TotalScore
	exam.PassScore = req.PassScore
	exam.Status = req.Status

	if err := database.DB.Save(&exam).Error; err != nil {
		logger.Errorf("更新试卷失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("更新试卷成功: %s", exam.Title)
	utils.SuccessWithMsg(c, "更新成功", exam.ToResponse())
}

// DeleteExam 删除试卷
func (ec *ExamController) DeleteExam(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的试卷ID")
		return
	}

	// 查找试卷
	var exam models.Exam
	if err := database.DB.First(&exam, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "试卷不存在")
		} else {
			logger.Errorf("查询试卷失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 检查是否有考核使用该试卷
	var assessmentCount int64
	if err := database.DB.Model(&models.AssessmentVideo{}).Where("exam_id = ?", uint(id)).Count(&assessmentCount).Error; err != nil {
		logger.Errorf("检查试卷使用情况失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if assessmentCount > 0 {
		utils.BadRequest(c, "该试卷已被考核使用，无法删除")
		return
	}

	// 开始事务
	tx := database.DB.Begin()

	// 删除试卷题目关联
	if err := tx.Where("exam_id = ?", uint(id)).Delete(&models.ExamQuestion{}).Error; err != nil {
		tx.Rollback()
		logger.Errorf("删除试卷题目关联失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 软删除试卷
	if err := tx.Delete(&exam).Error; err != nil {
		tx.Rollback()
		logger.Errorf("删除试卷失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	tx.Commit()

	logger.Infof("删除试卷成功: %s", exam.Title)
	utils.SuccessWithMsg(c, "删除成功", nil)
}

// UpdateExamQuestions 更新试卷题目
func (ec *ExamController) UpdateExamQuestions(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的试卷ID")
		return
	}

	var req models.ExamQuestionsUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查找试卷
	var exam models.Exam
	if err := database.DB.First(&exam, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "试卷不存在")
		} else {
			logger.Errorf("查询试卷失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 验证题目是否存在
	var questionIDs []uint
	for _, q := range req.Questions {
		questionIDs = append(questionIDs, q.QuestionID)
	}

	var questionCount int64
	if err := database.DB.Model(&models.Question{}).Where("id IN ? AND status = 1", questionIDs).Count(&questionCount).Error; err != nil {
		logger.Errorf("验证题目失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if int(questionCount) != len(questionIDs) {
		utils.BadRequest(c, "存在无效的题目")
		return
	}

	// 开始事务
	tx := database.DB.Begin()

	// 删除原有的题目关联
	if err := tx.Where("exam_id = ?", uint(id)).Delete(&models.ExamQuestion{}).Error; err != nil {
		tx.Rollback()
		logger.Errorf("删除原有题目关联失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 添加新的题目关联
	for i, q := range req.Questions {
		examQuestion := models.ExamQuestion{
			ExamID:     uint(id),
			QuestionID: q.QuestionID,
			Score:      q.Score,
			Sort:       q.Sort,
		}
		if examQuestion.Sort == 0 {
			examQuestion.Sort = i + 1
		}

		if err := tx.Create(&examQuestion).Error; err != nil {
			tx.Rollback()
			logger.Errorf("创建题目关联失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	tx.Commit()

	logger.Infof("更新试卷题目成功: %s", exam.Title)
	utils.SuccessWithMsg(c, "更新成功", nil)
}
