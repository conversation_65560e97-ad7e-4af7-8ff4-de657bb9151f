package models

import (
	"time"

	"gorm.io/gorm"
)

// Teacher 教师模型
type Teacher struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Name      string         `json:"name" gorm:"type:varchar(100);not null;comment:教师姓名"`
	Avatar    string         `json:"avatar" gorm:"type:varchar(500);comment:头像URL"`
	Mobile    string         `json:"mobile" gorm:"type:varchar(20);comment:手机号码"`
	Email     string         `json:"email" gorm:"type:varchar(100);comment:邮箱"`
	Position  string         `json:"position" gorm:"type:varchar(100);comment:职位"`
	Status    int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1启用 0禁用"`
	Sort      int            `json:"sort" gorm:"type:int;default:0;comment:排序，数字越小越靠前"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TeacherListRequest 教师列表请求参数
type TeacherListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Name     string `form:"name"`
	Mobile   string `form:"mobile"`
	Status   *int   `form:"status" binding:"omitempty,oneof=0 1"`
}

// TeacherCreateRequest 创建教师请求
type TeacherCreateRequest struct {
	Name     string `json:"name" binding:"required,max=100"`
	Avatar   string `json:"avatar"`
	Mobile   string `json:"mobile" binding:"omitempty,max=20"`
	Email    string `json:"email" binding:"omitempty,email,max=100"`
	Position string `json:"position" binding:"omitempty,max=100"`
	Status   *int   `json:"status" binding:"omitempty,oneof=0 1"`
	Sort     *int   `json:"sort" binding:"omitempty,min=0"`
}

// TeacherUpdateRequest 更新教师请求
type TeacherUpdateRequest struct {
	Name     *string `json:"name" binding:"omitempty,max=100"`
	Avatar   *string `json:"avatar"`
	Mobile   *string `json:"mobile" binding:"omitempty,max=20"`
	Email    *string `json:"email" binding:"omitempty,email,max=100"`
	Position *string `json:"position" binding:"omitempty,max=100"`
	Status   *int    `json:"status" binding:"omitempty,oneof=0 1"`
	Sort     *int    `json:"sort" binding:"omitempty,min=0"`
}

// TeacherResponse 教师响应数据
type TeacherResponse struct {
	ID         uint      `json:"id"`
	Name       string    `json:"name"`
	Avatar     string    `json:"avatar"`
	Mobile     string    `json:"mobile"`
	Email      string    `json:"email"`
	Position   string    `json:"position"`
	Status     int       `json:"status"`
	StatusText string    `json:"status_text"`
	Sort       int       `json:"sort"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (t *Teacher) ToResponse() *TeacherResponse {
	statusText := "禁用"
	if t.Status == 1 {
		statusText = "启用"
	}

	return &TeacherResponse{
		ID:         t.ID,
		Name:       t.Name,
		Avatar:     t.Avatar,
		Mobile:     t.Mobile,
		Email:      t.Email,
		Position:   t.Position,
		Status:     t.Status,
		StatusText: statusText,
		Sort:       t.Sort,
		CreatedAt:  t.CreatedAt,
		UpdatedAt:  t.UpdatedAt,
	}
}
