// postcss.config.js
export default {
  plugins: {
    "postcss-px-to-viewport": {
      unitToConvert: "px", // 要转换的单位
      viewportWidth: 375, // 设计稿宽度（仅用于 vw 转换）      // 设计稿高度，用于 vh 转换
      unitPrecision: 5, // 小数精度
      propList: ["*"], // 转换哪些属性，['*'] 表示全部
      viewportUnit: "vw", // 转换后的单位设为 vh
      fontViewportUnit: "vw", // 字体单位
      selectorBlackList: [".ignore", ".no-vh"], // 不转换的类名
      minPixelValue: 1, // 小于等于这个值的 px 不转换
      mediaQuery: false, // 是否处理媒体查询里的 px
      replace: true, // 是否直接替换原 px（true），false 则保留副本
      exclude: [], // 忽略文件（可传正则数组）
    },
  },
};
