<template>
  <div class="profile-page">
    <!-- 头部 -->
    <van-nav-bar
      title="我的"
      fixed
      style="background-color: #1bb394; color: #fff"
    />

    <!-- 内容区域 -->
    <div class="content">
      <!-- 用户信息卡片 -->
      <div class="user-card">
        <div class="user-avatar">
          <van-image
            :src="userInfo.avatar || 'https://img.yzcdn.cn/vant/cat.jpeg'"
            width="60"
            height="60"
            round
            fit="cover"
          />
        </div>
        <div class="user-info">
          <div class="user-name">{{ userInfo.name || "用户" }}</div>
          <div class="user-desc">{{ userInfo.department || "暂无部门" }}</div>
        </div>
      </div>

      <!-- 学习统计 -->
      <div class="stats-section">
        <div class="section-title" style="margin-bottom: 10px">学习统计</div>
        <div class="stats-grid">
          <div class="stat-item">
            <van-icon name="video" color="#fff" size="28" />
            <div class="stat-value">{{ formatTime(stats.videoTime) }}</div>
            <div class="stat-label">观看视频时长</div>
          </div>
          <div class="stat-item practice-stat">
            <!-- <div class="stat-icon">📝</div> -->
            <van-icon name="todo-list" color="#fff" size="28" />
            <div class="stat-value">{{ stats.practiceCount }}</div>
            <div class="stat-label">刷题数量</div>
          </div>
        </div>
      </div>

      <!-- 最近考试和刷题 -->
      <div class="exam-section">
        <div class="section-title">最近记录</div>
        <van-tabs v-model:active="activeTab" @change="onTabChange">
          <van-tab title="最近考试" name="exam">
            <div v-if="recentExams.length > 0" class="exam-list">
              <div
                v-for="exam in recentExams"
                :key="exam.id"
                class="exam-item"
                @click="goToExamResult(exam.id)"
              >
                <div class="exam-info">
                  <div class="exam-title">{{ exam.title }}</div>
                  <div class="exam-time">
                    {{ formatDateTime(exam.examTime) }}
                  </div>
                </div>
                <div class="exam-result" :class="getResultClass(exam)">
                  <div class="score">{{ exam.score }}分</div>
                  <div class="status">{{ getResultText(exam) }}</div>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <van-empty description="暂无考试记录" />
            </div>
          </van-tab>
          <van-tab title="最近刷题" name="practice">
            <div v-if="recentPractices.length > 0" class="exam-list">
              <div
                v-for="practice in recentPractices"
                :key="practice.id"
                class="exam-item"
                @click="goToExamResult(practice.id)"
              >
                <div class="exam-info">
                  <div class="exam-title">{{ practice.title }}</div>
                  <div class="exam-time">
                    {{ formatDateTime(practice.examTime) }}
                  </div>
                </div>
                <div class="exam-result" :class="getResultClass(practice)">
                  <div class="score">{{ practice.score }}分</div>
                  <div class="status">{{ getResultText(practice) }}</div>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <van-empty description="暂无刷题记录" />
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useUserStore } from "@/stores/user";
import { useRouter } from "vue-router";

const userStore = useUserStore();
const router = useRouter();

// tab状态
const activeTab = ref("exam");

// 使用store中的数据
const userInfo = computed(() => userStore.userInfo);
const stats = computed(() => userStore.learningStats);
const recentExams = computed(() => userStore.getRecentExams());
const recentPractices = computed(() => userStore.getRecentPractices());

// 格式化时间（秒转换为小时分钟）
const formatTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  }
  return `${minutes}分钟`;
};

// 格式化日期时间
const formatDateTime = (timeStr: string) => {
  const date = new Date(timeStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 获取成绩样式类
const getResultClass = (examRecord: any) => {
  console.log("getResultClass - examRecord:", examRecord);
  console.log(
    "getResultClass - isPassed:",
    examRecord.isPassed,
    typeof examRecord.isPassed
  );
  return examRecord.isPassed ? "pass" : "fail";
};

// 获取成绩文本
const getResultText = (examRecord: any) => {
  console.log("getResultText - examRecord:", examRecord);
  console.log(
    "getResultText - isPassed:",
    examRecord.isPassed,
    typeof examRecord.isPassed
  );
  return examRecord.isPassed ? "及格" : "不及格";
};

// tab切换
const onTabChange = () => {
  // 可以在这里添加tab切换的逻辑
};

// 跳转到考试结果页面
const goToExamResult = (examRecordId: number) => {
  router.push(`/exam/result/${examRecordId}`);
};

// 获取用户数据
const fetchUserData = async () => {
  await Promise.all([
    userStore.fetchUserInfo(),
    userStore.fetchLearningStats(),
    userStore.fetchExamRecords(),
    userStore.fetchPracticeRecords(),
  ]);
};

onMounted(() => {
  fetchUserData();
});
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 50px;
}
:deep(.van-nav-bar__title) {
  color: #fff;
}

.content {
  padding-top: 46px;
}

.user-card {
  background-color: #fff;
  padding: 20px;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.user-avatar {
  margin-right: 16px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.user-desc {
  font-size: 14px;
  color: #666;
}

.stats-section {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf0;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 20px 16px;
  background: linear-gradient(135deg, #1bb394 0%, #16a085 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(27, 179, 148, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  transform: translateY(0);
}

.stat-item:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(27, 179, 148, 0.3);
}

.stat-item.practice-stat {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
}

.stat-item.practice-stat:active {
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.stat-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  pointer-events: none;
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.stat-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.exam-section {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 12px;
}

/* .exam-list {
  margin-top: 16px;
} */

.exam-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebedf0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.exam-item:hover {
  background-color: #f8f9fa;
}

.exam-item:last-child {
  border-bottom: none;
}

.exam-info {
  flex: 1;
}

.exam-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.exam-time {
  font-size: 12px;
  color: #999;
}

.exam-result {
  text-align: center;
  min-width: 60px;
}

.score {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 2px;
}

.status {
  font-size: 12px;
}

.exam-result.pass .score {
  color: #1bb394;
}

.exam-result.pass .status {
  color: #1bb394;
}

.exam-result.fail .score {
  color: #f5222d;
}

.exam-result.fail .status {
  color: #f5222d;
}

.empty-state {
  padding: 40px 0;
}

.menu-section {
  margin-bottom: 12px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-cell__title) {
  font-size: 15px;
}

/* Tab样式 */
:deep(.van-tabs__nav) {
  background-color: #f8f9fa;
}

:deep(.van-tab) {
  color: #666;
  font-size: 14px;
}

:deep(.van-tab--active) {
  color: #1bb394;
  font-weight: 500;
}

:deep(.van-tabs__line) {
  background-color: #1bb394;
}

:deep(.van-tabs__content) {
  padding-top: 16px;
}
</style>
