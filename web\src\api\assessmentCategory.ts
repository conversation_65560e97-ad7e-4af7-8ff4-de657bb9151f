import request from '@/utils/request'
import type { 
  AssessmentCategory, 
  AssessmentCategoryListRequest, 
  AssessmentCategoryCreateRequest, 
  AssessmentCategoryUpdateRequest 
} from '@/types/assessmentCategory'

// 获取考核分类列表
export function getAssessmentCategoryList(params: AssessmentCategoryListRequest) {
  return request({
    url: '/admin/assessment-categories',
    method: 'get',
    params
  })
}

// 获取考核分类树
export function getAssessmentCategoryTree() {
  return request({
    url: '/admin/assessment-categories/tree',
    method: 'get'
  })
}

// 获取考核分类详情
export function getAssessmentCategory(id: number) {
  return request({
    url: `/admin/assessment-categories/${id}`,
    method: 'get'
  })
}

// 创建考核分类
export function createAssessmentCategory(data: AssessmentCategoryCreateRequest) {
  return request({
    url: '/admin/assessment-categories',
    method: 'post',
    data
  })
}

// 更新考核分类
export function updateAssessmentCategory(id: number, data: AssessmentCategoryUpdateRequest) {
  return request({
    url: `/admin/assessment-categories/${id}`,
    method: 'put',
    data
  })
}

// 删除考核分类
export function deleteAssessmentCategory(id: number) {
  return request({
    url: `/admin/assessment-categories/${id}`,
    method: 'delete'
  })
}
