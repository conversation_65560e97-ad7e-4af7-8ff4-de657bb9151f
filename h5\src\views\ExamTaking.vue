<template>
  <div class="exam-taking-page">
    <!-- 答题卡页面 -->
    <div v-if="showAnswerCard" class="answer-card-page">
      <!-- 头部 -->
      <div class="answer-card-header van-hairline--bottom">
        <van-icon name="arrow-left" @click="showAnswerCard = false" size="20" />
        <div class="header-center">
          <van-icon name="clock-o" color="#01997A" size="26" />
          <div class="timer">
            <van-count-down
              v-if="timeLeft > 0"
              :time="timeLeft * 1000"
              format="00:mm:ss"
              @finish="handleTimeUp"
            />

            <span v-else>00:00</span>
          </div>
        </div>
      </div>

      <!-- 题目网格 -->
      <div class="question-grid">
        <div
          v-for="(question, index) in questions"
          :key="question.question_id"
          class="question-number"
          :class="{
            answered: examTakingStore.getAnswer(
              examRecordId,
              question.question_id
            ),
            current: index === currentIndex,
          }"
          @click="goToQuestion(index)"
        >
          {{ index + 1 }}
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button
          type="primary"
          block
          round
          @click="showSubmitConfirm"
          style="background-color: #1bb394; border-color: #1bb394; margin: 20px"
        >
          交卷
        </van-button>
      </div>
    </div>

    <!-- 答题页面 -->
    <div v-else class="question-page">
      <!-- 头部导航 -->
      <div class="question-header van-hairline--bottom">
        <!-- <van-icon name="arrow-left" @click="handleBackClick" /> -->
        <van-icon name="clock-o" color="#01997A" size="26" />
        <div class="timer">
          <van-count-down
            v-if="timeLeft > 0"
            :time="timeLeft * 1000"
            format="00:mm:ss"
            @finish="handleTimeUp"
          />
          <span v-else>00:00</span>
        </div>
      </div>

      <!-- 题目进度 -->
      <div class="question-progress">
        <span>{{ currentIndex + 1 }}/{{ questions.length }}</span>
        <div class="answer-card-icon" @click="showAnswerCard = true">
          <van-icon name="records-o" color="#777777" size="20" />
          <span>答题卡</span>
        </div>
      </div>

      <!-- 题目内容 -->
      <div class="question-container" v-if="currentQuestion">
        <div class="question-type">
          {{ getQuestionType(currentQuestion.question.type) }}
        </div>
        <div class="question-content">
          {{ currentQuestion.question.content }}
        </div>

        <!-- 选项列表 -->
        <div class="options-list">
          <div
            v-for="option in currentQuestion.question.options"
            :key="option.label"
            class="option-item"
            :class="{
              'option-selected':
                examTakingStore.getAnswer(
                  examRecordId,
                  currentQuestion.question_id
                ) === option.label,
            }"
            @click="selectOption(currentQuestion.question_id, option.label)"
          >
            <div class="option-label">{{ option.label }}</div>
            <div class="option-content">{{ option.content }}</div>
          </div>
        </div>
      </div>

      <!-- 底部导航 -->
      <div class="bottom-nav">
        <van-button
          type="default"
          size="normal"
          :disabled="currentIndex === 0"
          @click="prevQuestion"
          class="nav-button"
        >
          上一题
        </van-button>
        <van-button
          type="primary"
          size="normal"
          @click="nextQuestion"
          class="nav-button"
          style="background-color: #1bb394; border-color: #1bb394"
        >
          {{ currentIndex < questions.length - 1 ? "下一题" : "答题卡" }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast, showDialog } from "vant";
import {
  getExamDetail,
  submitExam,
  type ExamDetailResponse,
  type ExamQuestionDetail,
} from "@/api/user";
import { useExamTakingStore } from "@/stores/practice";

const route = useRoute();
const router = useRouter();
const examTakingStore = useExamTakingStore();

// 考试详情
const examDetail = ref<ExamDetailResponse | null>(null);
const questions = ref<ExamQuestionDetail[]>([]);
const currentIndex = ref(0);
const timeLeft = ref(0);
const examRecordId = ref<number>(0);
const isSubmitting = ref(false);
const showAnswerCard = ref(false);

// 获取用户答案的计算属性
const userAnswers = computed(() => {
  return examTakingStore.getExamAnswers(examRecordId.value);
});

// 当前题目
const currentQuestion = computed(() => {
  if (questions.value.length === 0) return null;
  return questions.value[currentIndex.value];
});

// 初始化考试
const initExam = async () => {
  try {
    const id = Number(route.params.id);
    examRecordId.value = id;

    const response = await getExamDetail(id);
    if (response.code === 200 && response.data) {
      examDetail.value = response.data;
      questions.value = response.data.questions.sort((a, b) => a.sort - b.sort);
      timeLeft.value = response.data.time_left;

      // 初始化已答题目到store
      const savedAnswers: Record<number, string> = {};
      questions.value.forEach((q) => {
        if (q.user_answer) {
          savedAnswers[q.question_id] = q.user_answer;
        }
      });
      examTakingStore.initExamAnswers(examRecordId.value, savedAnswers);
    } else {
      showToast(response.msg || "获取考试详情失败");
      router.push("/exam");
    }
  } catch (error) {
    console.error("获取考试详情失败:", error);
    showToast("获取考试详情失败");
    router.push("/exam");
  }
};

// 获取题目类型文本
const getQuestionType = (type: number): string => {
  switch (type) {
    case 1:
      return "单选题";
    default:
      return "单选题";
  }
};

// 选择选项
const selectOption = (questionId: number, answer: string) => {
  // 保存答案到store
  examTakingStore.setAnswer(examRecordId.value, questionId, answer);

  // 自动跳转到下一题
  if (currentIndex.value < questions.value.length - 1) {
    setTimeout(() => {
      nextQuestion();
    }, 300);
  } else {
    // 最后一题答完后跳转到答题卡
    setTimeout(() => {
      showAnswerCard.value = true;
    }, 300);
  }
};

// 上一题
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
};

// 下一题
const nextQuestion = () => {
  if (currentIndex.value < questions.value.length - 1) {
    currentIndex.value++;
  } else {
    // 最后一题点击下一题时显示答题卡
    showAnswerCard.value = true;
  }
};

// 跳转到指定题目
const goToQuestion = (index: number) => {
  currentIndex.value = index;
  showAnswerCard.value = false;
};

// 时间到
const handleTimeUp = () => {
  showToast("考试时间已到，系统将自动提交");
  handleSubmit("timeout");
};

// 提交确认
const showSubmitConfirm = () => {
  // 检查是否有未答题目
  const currentAnswers = examTakingStore.getExamAnswers(examRecordId.value);
  const unansweredCount = questions.value.filter(
    (q) => !currentAnswers[q.question_id]
  ).length;

  let message = "确定要提交试卷吗？";
  if (unansweredCount > 0) {
    message = `您还有 ${unansweredCount} 题未作答，确定要提交吗？`;
  }

  showDialog({
    title: "提交确认",
    message,
    showCancelButton: true,
    confirmButtonText: "确定提交",
    confirmButtonColor: "#1bb394",
  }).then(() => {
    handleSubmit("manual");
  });
};

// 提交试卷
const handleSubmit = async (submitType: string) => {
  if (isSubmitting.value) return;

  isSubmitting.value = true;
  try {
    // 从store获取答案数据
    const currentAnswers = examTakingStore.getExamAnswers(examRecordId.value);
    const answers = Object.entries(currentAnswers).map(
      ([questionId, answer]) => ({
        question_id: Number(questionId),
        answer,
      })
    );

    const response = await submitExam({
      exam_record_id: examRecordId.value,
      answers,
      submit_type: submitType,
    });

    if (response.code === 200) {
      showToast("提交成功");
      // 清除store中的答案
      examTakingStore.clearExamAnswers(examRecordId.value);
      // 跳转到结果页面
      router.replace(`/exam/result/${examRecordId.value}`);
    } else {
      showToast(response.msg || "提交失败");
    }
  } catch (error) {
    console.error("提交试卷失败:", error);
    showToast("提交失败，请重试");
  } finally {
    isSubmitting.value = false;
  }
};

// 返回按钮处理
const handleBackClick = () => {
  // 显示答题卡
  showAnswerCard.value = true;
};

// 页面可见性变化监听（切换页面自动提交）
let visibilityTimer: number | null = null;
const handleVisibilityChange = () => {
  if (document.visibilityState === "hidden") {
    // 页面隐藏，启动定时器
    visibilityTimer = setTimeout(() => {
      console.log("页面隐藏超过5秒，自动提交考试");
      handleSubmit("switch");
    }, 5000); // 5秒后自动提交
  } else if (document.visibilityState === "visible") {
    // 页面显示，清除定时器
    if (visibilityTimer) {
      clearTimeout(visibilityTimer);
      visibilityTimer = null;
    }
  }
};

// 页面卸载前
const handleBeforeUnload = (e: BeforeUnloadEvent) => {
  const message = "离开页面将丢失当前作答，确定要离开吗？";
  e.preventDefault();
  return message;
};

// 定时检查考试状态
let statusCheckTimer: number | null = null;
const startStatusCheck = () => {
  statusCheckTimer = setInterval(async () => {
    try {
      const response = await getExamDetail(examRecordId.value);
      if (response.code === 200 && response.data) {
        timeLeft.value = response.data.time_left;

        // 如果时间已到，自动提交
        if (response.data.time_left <= 0) {
          handleTimeUp();
          return;
        }

        // 检查考试状态是否已结束
        if (response.data.exam_record.status !== 1) {
          // 1 = 进行中
          showToast("考试已结束");
          router.push("/exam");
          return;
        }
      }
    } catch (error) {
      console.error("检查考试状态失败:", error);
    }
  }, 30000); // 每30秒检查一次
};

const stopStatusCheck = () => {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer);
    statusCheckTimer = null;
  }
};

onMounted(() => {
  initExam();
  startStatusCheck();
  //document.addEventListener("visibilitychange", handleVisibilityChange);
  //window.addEventListener("beforeunload", handleBeforeUnload);
});

onBeforeUnmount(() => {
  stopStatusCheck();
  if (visibilityTimer) {
    clearTimeout(visibilityTimer);
  }
  //document.removeEventListener("visibilitychange", handleVisibilityChange);
  //window.removeEventListener("beforeunload", handleBeforeUnload);
});
</script>

<style scoped>
.exam-taking-page {
  min-height: 100vh;
  background-color: #fff;
  position: relative;
}
:deep(.van-count-down) {
  color: #2d2d2d;
}
/* 答题卡页面样式 */
.answer-card-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 16px;
  border-radius: 8px;
}

.answer-card-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 10px;
  padding: 10px 0;
  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
  }
}

.answer-card-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #1bb394;
  margin: 16px 0;
}

.question-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  padding: 16px;
}

.question-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #5a5a5a;
  cursor: pointer;
}

.question-number.answered {
  background-color: #01997a;
  color: #fff;
}

.question-number.current {
  background-color: #ff976a;
  color: #fff;
}

/* 答题页面样式 */
.question-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
  padding-bottom: 60px;
}

.question-header {
  display: flex;
  justify-content: center;
  gap: 6px;
  align-items: center;
  padding-bottom: 10px;
}

.timer {
  display: flex;
  align-items: center;
  color: #1bb394;
  font-weight: bold;
}

.question-progress {
  color: #666;
  margin: 10px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.answer-card-icon {
  display: flex;
  align-items: center;
  gap: 2px;
  font-weight: bold;
  cursor: pointer;
  span {
    color: #777777;
    font-size: 12px;
  }
}

.question-container {
  flex: 1;
}
.submit-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.question-type {
  display: inline-block;
  background-color: #e8f7f3;
  color: #1bb394;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  margin-bottom: 16px;
}

.question-content {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 12px;
}

.option-selected {
  background-color: #e8f7f3;
  border-color: #1bb394;
}

.option-label {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #5a5a5a;
  border: #afafaf solid 1px;
}

.option-selected .option-label {
  background-color: #1bb394;
  color: #fff;
  border: none;
}

.option-content {
  flex: 1;
}

.bottom-nav {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
  margin-top: auto;
}

.nav-button {
  width: 45%;
  border-radius: 24px;
}
</style>
