package logger

import (
	"ai_select_admin/config"
	"io"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

var Logger *logrus.Logger
var once sync.Once

func Init() {
	once.Do(func() {
		Logger = logrus.New()

		// 设置日志级别
		level, err := logrus.ParseLevel(config.AppConfig.Log.Level)
		if err != nil {
			level = logrus.InfoLevel
		}
		Logger.SetLevel(level)

		// 设置日志格式
		Logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
		_, err = os.Stat(config.AppConfig.Log.Dir)

		if os.IsNotExist(err) {
			if err = os.MkdirAll(config.AppConfig.Log.Dir, 0755); err != nil {
				log.Fatalf("创建日志目录失败: %v\n", err)
			}
			//创建文件成功
		}

		lumberjackLogger := &lumberjack.Logger{
			Filename:   filepath.Join(config.AppConfig.Log.Dir, time.Now().Format("2006-01-02")+".log"),
			MaxSize:    config.AppConfig.Log.MaxSize,
			MaxBackups: config.AppConfig.Log.MaxBackups,
			MaxAge:     config.AppConfig.Log.MaxAge,
			Compress:   config.AppConfig.Log.Compress,
		}

		if config.AppConfig.Mode == "debug" {
			Logger.SetReportCaller(true)
			Logger.SetOutput(io.MultiWriter(os.Stdout, lumberjackLogger))
		} else {
			Logger.SetOutput(lumberjackLogger)
		}
	})
}

// Info 记录信息日志
func Info(args ...interface{}) {
	Logger.Info(args...)
}

// Infof 记录格式化信息日志
func Infof(format string, args ...interface{}) {
	Logger.Infof(format, args...)
}

// Error 记录错误日志
func Error(args ...interface{}) {
	Logger.Error(args...)
}

// Errorf 记录格式化错误日志
func Errorf(format string, args ...interface{}) {
	Logger.Errorf(format, args...)
}

// Warn 记录警告日志
func Warn(args ...interface{}) {
	Logger.Warn(args...)
}

// Warnf 记录格式化警告日志
func Warnf(format string, args ...interface{}) {
	Logger.Warnf(format, args...)
}

// Debug 记录调试日志
func Debug(args ...interface{}) {
	Logger.Debug(args...)
}

// Debugf 记录格式化调试日志
func Debugf(format string, args ...interface{}) {
	Logger.Debugf(format, args...)
}
