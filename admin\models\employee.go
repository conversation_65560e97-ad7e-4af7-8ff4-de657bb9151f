package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// EmployeeStatus 员工状态
type EmployeeStatus int

const (
	EmployeeStatusActive   EmployeeStatus = 1 // 已激活
	EmployeeStatusInactive EmployeeStatus = 2 // 已禁用
	EmployeeStatusNotJoin  EmployeeStatus = 4 // 未加入企业
	EmployeeStatusQuit     EmployeeStatus = 5 // 退出企业
)

// Employee 员工模型
type Employee struct {
	ID             uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID         string         `json:"user_id" gorm:"type:varchar(100);not null;uniqueIndex;comment:企业微信用户ID"`
	Name           string         `json:"name" gorm:"type:varchar(100);not null;comment:员工姓名"`
	Position       string         `json:"position" gorm:"type:varchar(100);comment:职位"`
	Mobile         string         `json:"mobile" gorm:"type:varchar(20);comment:手机号"`
	Gender         string         `json:"gender" gorm:"type:varchar(10);comment:性别"`
	Email          string         `json:"email" gorm:"type:varchar(100);comment:邮箱"`
	Avatar         string         `json:"avatar" gorm:"type:varchar(500);comment:头像URL"`
	Telephone      string         `json:"telephone" gorm:"type:varchar(50);comment:座机"`
	Alias          string         `json:"alias" gorm:"type:varchar(100);comment:别名"`
	Address        string         `json:"address" gorm:"type:varchar(500);comment:地址"`
	OpenUserID     string         `json:"open_userid" gorm:"type:varchar(100);comment:全局唯一用户ID"`
	MainDeptID     int            `json:"main_dept_id" gorm:"type:int;comment:主部门ID"`
	Status         EmployeeStatus `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1已激活 2已禁用 4未加入企业 5退出企业"`
	IsLeaderInDept string         `json:"-" gorm:"type:json;comment:在所在部门是否为部门负责人JSON"`
	IsLeaderData   []int          `json:"is_leader_in_dept" gorm:"-"`
	Department     string         `json:"-" gorm:"type:json;comment:所在部门列表JSON"`
	DepartmentData []int          `json:"department" gorm:"-"`
	Order          string         `json:"-" gorm:"type:json;comment:部门内的排序值JSON"`
	OrderData      []int          `json:"order" gorm:"-"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Departments []Department `json:"departments,omitempty" gorm:"many2many:employee_departments;foreignKey:ID;joinForeignKey:EmployeeID;References:WechatID;joinReferences:DepartmentWechatID"`
}

// BeforeSave GORM钩子：保存前处理
func (e *Employee) BeforeSave(tx *gorm.DB) error {
	if len(e.IsLeaderData) > 0 {
		isLeaderJSON, err := json.Marshal(e.IsLeaderData)
		if err != nil {
			return err
		}
		e.IsLeaderInDept = string(isLeaderJSON)
	}

	if len(e.DepartmentData) > 0 {
		deptJSON, err := json.Marshal(e.DepartmentData)
		if err != nil {
			return err
		}
		e.Department = string(deptJSON)
	}

	if len(e.OrderData) > 0 {
		orderJSON, err := json.Marshal(e.OrderData)
		if err != nil {
			return err
		}
		e.Order = string(orderJSON)
	}

	return nil
}

// AfterFind GORM钩子：查询后处理
func (e *Employee) AfterFind(tx *gorm.DB) error {
	if e.IsLeaderInDept != "" {
		if err := json.Unmarshal([]byte(e.IsLeaderInDept), &e.IsLeaderData); err != nil {
			return err
		}
	}

	if e.Department != "" {
		if err := json.Unmarshal([]byte(e.Department), &e.DepartmentData); err != nil {
			return err
		}
	}

	if e.Order != "" {
		if err := json.Unmarshal([]byte(e.Order), &e.OrderData); err != nil {
			return err
		}
	}

	return nil
}

// EmployeeListRequest 员工列表请求参数
type EmployeeListRequest struct {
	Page         int             `form:"page" binding:"omitempty,min=1"`
	PageSize     int             `form:"page_size" binding:"omitempty,min=1,max=100"`
	Name         string          `form:"name"`
	DepartmentID *int            `form:"department_id"`
	Status       *EmployeeStatus `form:"status" binding:"omitempty,oneof=1 2 4 5"`
}

// EmployeeSearchRequest 员工搜索请求参数（用于下拉选择）
type EmployeeSearchRequest struct {
	Page         int    `form:"page" binding:"omitempty,min=1"`
	PageSize     int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Keyword      string `form:"keyword"`       // 搜索关键字（姓名）
	DepartmentID *int   `form:"department_id"` // 部门ID筛选
}

// EmployeeResponse 员工响应数据
type EmployeeResponse struct {
	ID             uint                 `json:"id"`
	UserID         string               `json:"user_id"`
	Name           string               `json:"name"`
	Position       string               `json:"position"`
	Mobile         string               `json:"mobile"`
	Gender         string               `json:"gender"`
	Email          string               `json:"email"`
	Avatar         string               `json:"avatar"`
	Telephone      string               `json:"telephone"`
	Alias          string               `json:"alias"`
	Address        string               `json:"address"`
	OpenUserID     string               `json:"open_userid"`
	MainDeptID     int                  `json:"main_dept_id"`
	Status         EmployeeStatus       `json:"status"`
	StatusText     string               `json:"status_text"`
	IsLeaderInDept []int                `json:"is_leader_in_dept"`
	Department     []int                `json:"department"`
	Order          []int                `json:"order"`
	Departments    []DepartmentResponse `json:"departments,omitempty"`
	CreatedAt      time.Time            `json:"created_at"`
	UpdatedAt      time.Time            `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (e *Employee) ToResponse() *EmployeeResponse {
	statusText := ""
	switch e.Status {
	case EmployeeStatusActive:
		statusText = "已激活"
	case EmployeeStatusInactive:
		statusText = "已禁用"
	case EmployeeStatusNotJoin:
		statusText = "未加入企业"
	case EmployeeStatusQuit:
		statusText = "退出企业"
	}

	resp := &EmployeeResponse{
		ID:             e.ID,
		UserID:         e.UserID,
		Name:           e.Name,
		Position:       e.Position,
		Mobile:         e.Mobile,
		Gender:         e.Gender,
		Email:          e.Email,
		Avatar:         e.Avatar,
		Telephone:      e.Telephone,
		Alias:          e.Alias,
		Address:        e.Address,
		OpenUserID:     e.OpenUserID,
		MainDeptID:     e.MainDeptID,
		Status:         e.Status,
		StatusText:     statusText,
		IsLeaderInDept: e.IsLeaderData,
		Department:     e.DepartmentData,
		Order:          e.OrderData,
		CreatedAt:      e.CreatedAt,
		UpdatedAt:      e.UpdatedAt,
	}

	// 转换部门信息
	if len(e.Departments) > 0 {
		resp.Departments = make([]DepartmentResponse, len(e.Departments))
		for i, dept := range e.Departments {
			resp.Departments[i] = *dept.ToResponse()
		}
	}

	return resp
}
