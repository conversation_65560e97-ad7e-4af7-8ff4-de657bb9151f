package controllers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"ai_select_admin/database"
	"ai_select_admin/models"
	"ai_select_admin/utils"
)

type TeacherController struct{}

// ListTeachers 获取教师列表
func (tc *TeacherController) ListTeachers(c *gin.Context) {
	var req models.TeacherListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	var teachers []models.Teacher
	var total int64

	// 构建查询
	query := database.DB.Model(&models.Teacher{})

	// 添加搜索条件
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Mobile != "" {
		query = query.Where("mobile LIKE ?", "%"+req.Mobile+"%")
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		utils.InternalServerError(c, "查询失败")
		return
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("sort ASC, created_at DESC").
		Offset(offset).Limit(req.PageSize).
		Find(&teachers).Error; err != nil {
		utils.InternalServerError(c, "查询失败")
		return
	}

	// 转换为响应格式
	var teacherResponses []*models.TeacherResponse
	for _, teacher := range teachers {
		teacherResponses = append(teacherResponses, teacher.ToResponse())
	}

	// 计算分页信息
	pages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	pageInfo := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Total:   total,
		Pages:   pages,
	}

	utils.PageSuccess(c, teacherResponses, pageInfo)
}

// GetTeacher 获取教师详情
func (tc *TeacherController) GetTeacher(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的教师ID")
		return
	}

	var teacher models.Teacher
	if err := database.DB.First(&teacher, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "教师不存在")
			return
		}
		utils.InternalServerError(c, "查询失败")
		return
	}

	utils.Success(c, teacher.ToResponse())
}

// CreateTeacher 创建教师
func (tc *TeacherController) CreateTeacher(c *gin.Context) {
	var req models.TeacherCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 检查教师名称是否已存在
	var existingTeacher models.Teacher
	if err := database.DB.Where("name = ?", req.Name).First(&existingTeacher).Error; err == nil {
		utils.BadRequest(c, "教师名称已存在")
		return
	}

	// 创建教师
	teacher := models.Teacher{
		Name:     req.Name,
		Avatar:   req.Avatar,
		Mobile:   req.Mobile,
		Email:    req.Email,
		Position: req.Position,
		Status:   1, // 默认启用
		Sort:     0, // 默认排序
	}

	// 设置可选字段
	if req.Status != nil {
		teacher.Status = *req.Status
	}
	if req.Sort != nil {
		teacher.Sort = *req.Sort
	}

	if err := database.DB.Create(&teacher).Error; err != nil {
		utils.InternalServerError(c, "创建失败")
		return
	}

	utils.SuccessWithMsg(c, "创建成功", teacher.ToResponse())
}

// UpdateTeacher 更新教师
func (tc *TeacherController) UpdateTeacher(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的教师ID")
		return
	}

	var req models.TeacherUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查找教师
	var teacher models.Teacher
	if err := database.DB.First(&teacher, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "教师不存在")
			return
		}
		utils.InternalServerError(c, "查询失败")
		return
	}

	// 检查名称是否重复（如果要更新名称）
	if req.Name != nil && *req.Name != teacher.Name {
		var existingTeacher models.Teacher
		if err := database.DB.Where("name = ? AND id != ?", *req.Name, id).First(&existingTeacher).Error; err == nil {
			utils.BadRequest(c, "教师名称已存在")
			return
		}
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Avatar != nil {
		updates["avatar"] = *req.Avatar
	}
	if req.Mobile != nil {
		updates["mobile"] = *req.Mobile
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Position != nil {
		updates["position"] = *req.Position
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.Sort != nil {
		updates["sort"] = *req.Sort
	}

	if err := database.DB.Model(&teacher).Updates(updates).Error; err != nil {
		utils.InternalServerError(c, "更新失败")
		return
	}

	// 重新查询更新后的数据
	if err := database.DB.First(&teacher, id).Error; err != nil {
		utils.InternalServerError(c, "查询失败")
		return
	}

	utils.SuccessWithMsg(c, "更新成功", teacher.ToResponse())
}

// DeleteTeacher 删除教师
func (tc *TeacherController) DeleteTeacher(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的教师ID")
		return
	}

	// 查找教师
	var teacher models.Teacher
	if err := database.DB.First(&teacher, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "教师不存在")
			return
		}
		utils.InternalServerError(c, "查询失败")
		return
	}

	// 软删除
	if err := database.DB.Delete(&teacher).Error; err != nil {
		utils.InternalServerError(c, "删除失败")
		return
	}

	utils.SuccessWithMsg(c, "删除成功", nil)
}

// SearchTeachers 搜索教师（用于下拉选择）
func (tc *TeacherController) SearchTeachers(c *gin.Context) {
	keyword := c.Query("keyword")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	var teachers []models.Teacher
	var total int64

	query := database.DB.Model(&models.Teacher{}).Where("status = 1") // 只查询启用的教师

	if keyword != "" {
		query = query.Where("name LIKE ? OR mobile LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		utils.InternalServerError(c, "查询失败")
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("sort ASC, name ASC").
		Offset(offset).Limit(pageSize).
		Find(&teachers).Error; err != nil {
		utils.InternalServerError(c, "查询失败")
		return
	}

	// 转换为响应格式
	var teacherResponses []*models.TeacherResponse
	for _, teacher := range teachers {
		teacherResponses = append(teacherResponses, teacher.ToResponse())
	}

	// 计算分页信息
	pages := int((total + int64(pageSize) - 1) / int64(pageSize))
	pageInfo := utils.PageInfo{
		Current: page,
		Size:    pageSize,
		Total:   total,
		Pages:   pages,
	}

	utils.PageSuccess(c, teacherResponses, pageInfo)
}
