# 考试功能实现说明

## 功能概述

本次实现了完整的在线考试功能，包括：

1. **考试答题界面** - 支持选择题答题，题目导航，自动保存答案
2. **考试计时功能** - 倒计时显示，超时自动提交
3. **自动提交机制** - 超时提交、切换页面提交、手动提交
4. **自动批改功能** - 考试完成后自动批改并计算得分
5. **考试结果展示** - 显示得分、每题对错详情
6. **考试状态管理** - 防止重复考试，支持断点续答

## 数据库模型

### 考试记录表 (exam_records)
- `id` - 主键
- `employee_id` - 员工ID
- `exam_id` - 试卷ID
- `assessment_record_id` - 考核记录ID（可选）
- `status` - 考试状态（0未开始 1进行中 2已完成 3超时 4已取消）
- `start_time` - 开始时间
- `end_time` - 结束时间
- `submit_time` - 提交时间
- `duration` - 实际用时（秒）
- `score` - 得分
- `total_score` - 总分
- `pass_score` - 及格分
- `is_passed` - 是否及格
- `correct_count` - 正确题目数
- `total_count` - 总题目数

### 答题记录表 (exam_answers)
- `id` - 主键
- `exam_record_id` - 考试记录ID
- `question_id` - 题目ID
- `answer` - 用户答案
- `is_correct` - 是否正确
- `score` - 得分
- `answer_time` - 答题时间

## 后端API接口

### 1. 开始考试
- **接口**: `POST /api/h5/startExam`
- **参数**: 
  ```json
  {
    "exam_id": 1,
    "assessment_record_id": 1  // 可选
  }
  ```
- **返回**: 
  ```json
  {
    "code": 200,
    "data": {
      "exam_record_id": 1,
      "status": "started",
      "message": "考试开始"
    }
  }
  ```

### 2. 获取考试详情
- **接口**: `GET /api/h5/examDetail/{exam_record_id}`
- **返回**: 考试详情，包含题目列表、剩余时间等

### 3. 保存答案
- **接口**: `POST /api/h5/saveAnswer`
- **参数**: 
  ```json
  {
    "exam_record_id": 1,
    "question_id": 1,
    "answer": "A"
  }
  ```

### 4. 提交考试
- **接口**: `POST /api/h5/submitExam`
- **参数**: 
  ```json
  {
    "exam_record_id": 1,
    "answers": [
      {"question_id": 1, "answer": "A"},
      {"question_id": 2, "answer": "B"}
    ],
    "submit_type": "manual"  // manual/timeout/switch
  }
  ```

### 5. 获取考试结果
- **接口**: `GET /api/h5/examResult/{exam_record_id}`
- **返回**: 考试结果详情，包含每题对错情况

## 前端页面

### 1. 考试列表页面 (`/exam`)
- 显示可参与的考试和刷题试卷
- 支持考试/刷题切换
- 点击开始考试按钮进入答题页面

### 2. 考试答题页面 (`/exam/taking/{exam_record_id}`)
- 题目展示和选项选择
- 上一题/下一题导航
- 题目导航抽屉
- 倒计时显示
- 自动保存答案
- 提交确认

### 3. 考试结果页面 (`/exam/result/{exam_record_id}`)
- 考试得分和通过状态
- 每题详细对错情况
- 正确答案显示

## 核心功能特性

### 1. 考试计时
- 实时倒计时显示
- 超时自动提交
- 定时检查考试状态

### 2. 自动提交机制
- **超时提交**: 考试时间到达时自动提交
- **切换页面提交**: 页面隐藏超过5秒自动提交
- **手动提交**: 用户主动提交

### 3. 答案保存
- 选择选项后自动保存
- 支持修改答案
- 断点续答功能

### 4. 自动批改
- 选择题自动对比正确答案
- 计算总分和正确率
- 判断是否及格

### 5. 防作弊机制
- 页面切换监控
- 考试状态实时检查
- 防止重复考试

## 样式设计

使用主题色 `#1bb394` 进行设计：
- 导航栏背景色
- 按钮颜色
- 选中状态颜色
- 正确答案标识色

## 使用流程

1. **进入考试列表** - 查看可参与的考试
2. **开始考试** - 点击开始考试按钮，确认后进入答题页面
3. **答题过程** - 选择答案，系统自动保存，可使用导航切换题目
4. **提交考试** - 答题完成后提交，或时间到自动提交
5. **查看结果** - 自动跳转到结果页面，查看得分和详情

## 注意事项

1. 考试试卷只能考一次，刷题试卷可以重复练习
2. 考试过程中切换页面会触发自动提交机制
3. 系统会定时检查考试状态，防止异常情况
4. 所有答案都会实时保存，确保数据不丢失

## 技术栈

- **后端**: Go + Gin + GORM + MySQL
- **前端**: Vue 3 + TypeScript + Vant + Pinia
- **数据库**: MySQL 8.0+
