<template>
  <div class="teacher-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="教师姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入教师姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input
            v-model="searchForm.mobile"
            placeholder="请输入手机号码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleSearch"
            style="
              background-color: rgb(27, 179, 148);
              border-color: rgb(27, 179, 148);
            "
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button
            type="success"
            @click="handleAdd"
            style="background-color: #1bb394; border-color: #1bb394"
          >
            <el-icon><Plus /></el-icon>
            新增教师
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 教师列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="头像" width="80" align="center">
          <template #default="{ row }">
            <el-avatar :size="50" :src="row.avatar" :alt="row.name">
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="教师姓名" min-width="120" />
        <el-table-column prop="mobile" label="手机号码" min-width="120" />
        <el-table-column
          prop="email"
          label="邮箱"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column prop="position" label="职位" min-width="120" />
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formaterTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingId ? '编辑教师' : '新增教师'"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="教师姓名" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入教师姓名"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <div class="avatar-upload">
            <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              :before-upload="beforeAvatarUpload"
              :http-request="handleAvatarUpload"
              :disabled="uploadLoading"
              accept="image/*"
            >
              <div v-if="uploadLoading" class="avatar-uploading">
                <el-progress
                  style="margin-top: 10px"
                  type="circle"
                  :percentage="uploadProgress"
                  :width="60"
                  :stroke-width="6"
                />
                <div class="upload-text">上传中...</div>
              </div>
              <img
                v-else-if="formData.avatar"
                :src="formData.avatar"
                class="avatar"
              />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div class="upload-tip">支持 jpg、png 格式，文件大小不超过 2MB</div>
          </div>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input
            v-model="formData.mobile"
            placeholder="请输入手机号码"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="formData.email"
            placeholder="请输入邮箱"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-input
            v-model="formData.position"
            placeholder="请输入职位"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            :max="9999"
            placeholder="数字越小越靠前"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
            style="background-color: #1bb394; border-color: #1bb394"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { formaterTime } from "@/utils/functions";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type UploadRequestOptions,
} from "element-plus";
import { Search, Refresh, Plus, User } from "@element-plus/icons-vue";
import {
  getTeacherList,
  createTeacher,
  updateTeacher,
  deleteTeacher,
  type Teacher,
  type TeacherListRequest,
  type TeacherCreateRequest,
  type TeacherUpdateRequest,
} from "@/api/teacher";
import { uploadToCOS, validateFile } from "@/utils/cos";

// 搜索表单
const searchForm = reactive<TeacherListRequest>({
  name: "",
  mobile: "",
  status: undefined,
});

// 表格数据
const tableData = ref<Teacher[]>([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 对话框
const dialogVisible = ref(false);
const editingId = ref<number | null>(null);
const submitLoading = ref(false);
const uploadLoading = ref(false);
const uploadProgress = ref(0);

// 表单
const formRef = ref<FormInstance>();
const formData = reactive<TeacherCreateRequest & TeacherUpdateRequest>({
  name: "",
  avatar: "",
  mobile: "",
  email: "",
  position: "",
  status: 1,
  sort: 0,
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: "请输入教师姓名", trigger: "blur" },
    { max: 100, message: "教师姓名不能超过100个字符", trigger: "blur" },
  ],
  mobile: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

// 获取教师列表
const fetchTeacherList = async () => {
  try {
    loading.value = true;
    const params: TeacherListRequest = {
      page: pagination.current,
      page_size: pagination.size,
      ...searchForm,
    };
    const response = await getTeacherList(params);

    tableData.value = response.data || [];
    pagination.total = response.page.total || 0;
  } catch (error) {
    console.error("获取教师列表失败:", error);
    ElMessage.error("获取教师列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchTeacherList();
};

// 重置搜索
const handleReset = () => {
  searchForm.name = "";
  searchForm.mobile = "";
  searchForm.status = undefined;
  pagination.current = 1;
  fetchTeacherList();
};

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchTeacherList();
};

const handleCurrentChange = (page: number) => {
  pagination.current = page;
  fetchTeacherList();
};

// 新增教师
const handleAdd = () => {
  editingId.value = null;
  resetForm();
  dialogVisible.value = true;
};

// 编辑教师
const handleEdit = (row: Teacher) => {
  editingId.value = row.id;
  formData.name = row.name;
  formData.avatar = row.avatar;
  formData.mobile = row.mobile;
  formData.email = row.email;
  formData.position = row.position;
  formData.status = row.status;
  formData.sort = row.sort;
  dialogVisible.value = true;
};

// 删除教师
const handleDelete = async (row: Teacher) => {
  try {
    await ElMessageBox.confirm(`确定要删除教师"${row.name}"吗？`, "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await deleteTeacher(row.id);
    ElMessage.success("删除成功");
    fetchTeacherList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除教师失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 头像上传前验证
const beforeAvatarUpload = (file: File) => {
  try {
    validateFile(file, {
      maxSize: 2 * 1024 * 1024, // 2MB
      allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/bmp"],
    });
    return true;
  } catch (error) {
    ElMessage.error((error as Error).message);
    return false;
  }
};

// 头像上传
const handleAvatarUpload = async (options: UploadRequestOptions) => {
  try {
    uploadLoading.value = true;
    uploadProgress.value = 0;

    const file = options.file as File;
    const url = await uploadToCOS(file, (progress) => {
      uploadProgress.value = progress;
    });

    formData.avatar = url;
    ElMessage.success("头像上传成功");
  } catch (error) {
    console.error("头像上传失败:", error);
    ElMessage.error("头像上传失败");
  } finally {
    uploadLoading.value = false;
    uploadProgress.value = 0;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitLoading.value = true;

    if (editingId.value) {
      // 更新
      await updateTeacher(editingId.value, formData);
      ElMessage.success("更新成功");
    } else {
      // 创建
      await createTeacher(formData);
      ElMessage.success("创建成功");
    }

    dialogVisible.value = false;
    fetchTeacherList();
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  } finally {
    submitLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  formData.name = "";
  formData.avatar = "";
  formData.mobile = "";
  formData.email = "";
  formData.position = "";
  formData.status = 1;
  formData.sort = 0;
  formRef.value?.clearValidate();
};

// 对话框关闭
const handleDialogClose = () => {
  resetForm();
};

// 初始化
onMounted(() => {
  fetchTeacherList();
});
</script>

<style scoped>
.teacher-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.avatar-uploading {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-text {
  font-size: 12px;
  color: #666;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  object-fit: cover;
}

.avatar-uploader .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.avatar-uploader .avatar-uploader-icon:hover {
  border-color: #409eff;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
