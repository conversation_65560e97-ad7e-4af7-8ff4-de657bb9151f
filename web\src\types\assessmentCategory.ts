// 考核分类
export interface AssessmentCategory {
  id: number
  name: string
  description: string
  sort: number
  status: number
  parent_id: number
  children?: AssessmentCategory[]
  created_at: string
  updated_at: string
}

// 考核分类列表请求参数
export interface AssessmentCategoryListRequest {
  page?: number
  page_size?: number
  name?: string
  status?: number
  parent_id?: number
}

// 创建考核分类请求参数
export interface AssessmentCategoryCreateRequest {
  name: string
  description?: string
  sort?: number
  status?: number
  parent_id?: number
}

// 更新考核分类请求参数
export interface AssessmentCategoryUpdateRequest {
  name: string
  description?: string
  sort?: number
  status?: number
  parent_id?: number
}
