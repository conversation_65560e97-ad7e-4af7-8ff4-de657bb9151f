package models

import (
	"time"

	"gorm.io/gorm"
)

// AssessmentVideo 考核视频模型
type AssessmentVideo struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title       string         `json:"title" gorm:"type:varchar(200);not null;comment:考核标题"`
	Description string         `json:"description" gorm:"type:text;comment:考核描述"`
	CategoryID  uint           `json:"category_id" gorm:"type:int;default:0;comment:考核分类ID"`
	ExamID      uint           `json:"exam_id" gorm:"type:int;default:0;comment:关联试卷ID"`
	Status      int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1启用 0禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Category      *AssessmentCategory       `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Exam          *Exam                     `json:"exam,omitempty" gorm:"foreignKey:ExamID"`
	Videos        []AssessmentVideoRelation `json:"videos,omitempty" gorm:"foreignKey:AssessmentVideoID"`
	Teachers      []AssessmentVideoTeacher  `json:"teachers,omitempty" gorm:"foreignKey:AssessmentVideoID"`
	PracticeExams []AssessmentVideoPractice `json:"practice_exams,omitempty" gorm:"foreignKey:AssessmentVideoID"`
}

// AssessmentVideoRelation 考核视频关联模型
type AssessmentVideoRelation struct {
	ID                uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	AssessmentVideoID uint           `json:"assessment_video_id" gorm:"type:int;not null;comment:考核视频ID"`
	VideoID           uint           `json:"video_id" gorm:"type:int;not null;comment:视频ID"`
	Sort              int            `json:"sort" gorm:"type:int;default:0;comment:视频顺序"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Video *Video `json:"video,omitempty" gorm:"foreignKey:VideoID"`
}

// AssessmentVideoTeacher 考核视频教师关联模型
type AssessmentVideoTeacher struct {
	ID                uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	AssessmentVideoID uint           `json:"assessment_video_id" gorm:"type:int;not null;comment:考核视频ID"`
	TeacherID         uint           `json:"teacher_id" gorm:"type:int;not null;comment:教师ID"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Teacher *Teacher `json:"teacher,omitempty" gorm:"foreignKey:TeacherID"`
}

// AssessmentVideoListRequest 考核视频列表请求参数
type AssessmentVideoListRequest struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Title      string `form:"title"`
	CategoryID *uint  `form:"category_id"`
	Status     *int   `form:"status" binding:"omitempty,oneof=0 1"`
}

// AssessmentVideoSearchRequest 考核视频搜索请求参数（用于下拉选择）
type AssessmentVideoSearchRequest struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Keyword    string `form:"keyword"`     // 搜索关键字（标题）
	CategoryID *uint  `form:"category_id"` // 分类ID筛选
}

// AssessmentVideoCreateRequest 创建考核视频请求参数
type AssessmentVideoCreateRequest struct {
	Title           string `json:"title" binding:"required,max=200"`
	Description     string `json:"description" binding:"max=1000"`
	CategoryID      uint   `json:"category_id" binding:"omitempty,min=0"`
	ExamID          uint   `json:"exam_id" binding:"omitempty,min=0"`
	VideoIDs        []uint `json:"video_ids" binding:"omitempty"`
	TeacherIDs      []uint `json:"teacher_ids" binding:"omitempty"`
	PracticeExamIDs []uint `json:"practice_exam_ids" binding:"omitempty"`
	Status          int    `json:"status" binding:"omitempty,oneof=0 1"`
}

// AssessmentVideoUpdateRequest 更新考核视频请求参数
type AssessmentVideoUpdateRequest struct {
	Title           string `json:"title" binding:"required,max=200"`
	Description     string `json:"description" binding:"max=1000"`
	CategoryID      uint   `json:"category_id" binding:"omitempty,min=0"`
	ExamID          uint   `json:"exam_id" binding:"omitempty,min=0"`
	VideoIDs        []uint `json:"video_ids" binding:"required,min=1"`
	TeacherIDs      []uint `json:"teacher_ids" binding:"omitempty"`
	PracticeExamIDs []uint `json:"practice_exam_ids" binding:"omitempty"`
	Status          int    `json:"status" binding:"omitempty,oneof=0 1"`
}

// AssessmentVideoResponse 考核视频响应数据
type AssessmentVideoResponse struct {
	ID                uint                              `json:"id"`
	Title             string                            `json:"title"`
	Description       string                            `json:"description"`
	CategoryID        uint                              `json:"category_id"`
	ExamID            uint                              `json:"exam_id"`
	Status            int                               `json:"status"`
	VideoCount        int                               `json:"video_count"`
	TeacherCount      int                               `json:"teacher_count"`
	PracticeExamCount int                               `json:"practice_exam_count"`
	Category          *AssessmentCategoryResponse       `json:"category,omitempty"`
	Exam              *ExamResponse                     `json:"exam,omitempty"`
	Videos            []AssessmentVideoRelationResponse `json:"videos,omitempty"`
	Teachers          []AssessmentVideoTeacherResponse  `json:"teachers,omitempty"`
	PracticeExams     []AssessmentVideoPracticeResponse `json:"practice_exams,omitempty"`
	CreatedAt         time.Time                         `json:"created_at"`
	UpdatedAt         time.Time                         `json:"updated_at"`
}

// AssessmentVideoRelationResponse 考核视频关联响应数据
type AssessmentVideoRelationResponse struct {
	ID                uint           `json:"id"`
	AssessmentVideoID uint           `json:"assessment_video_id"`
	VideoID           uint           `json:"video_id"`
	Sort              int            `json:"sort"`
	Video             *VideoResponse `json:"video,omitempty"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
}

// AssessmentVideoTeacherResponse 考核视频教师关联响应数据
type AssessmentVideoTeacherResponse struct {
	ID                uint             `json:"id"`
	AssessmentVideoID uint             `json:"assessment_video_id"`
	TeacherID         uint             `json:"teacher_id"`
	Teacher           *TeacherResponse `json:"teacher,omitempty"`
	CreatedAt         time.Time        `json:"created_at"`
	UpdatedAt         time.Time        `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (av *AssessmentVideo) ToResponse() *AssessmentVideoResponse {
	resp := &AssessmentVideoResponse{
		ID:                av.ID,
		Title:             av.Title,
		Description:       av.Description,
		CategoryID:        av.CategoryID,
		ExamID:            av.ExamID,
		Status:            av.Status,
		VideoCount:        len(av.Videos),
		TeacherCount:      len(av.Teachers),
		PracticeExamCount: len(av.PracticeExams),
		CreatedAt:         av.CreatedAt,
		UpdatedAt:         av.UpdatedAt,
	}

	if av.Category != nil {
		resp.Category = av.Category.ToResponse()
	}

	if av.Exam != nil {
		resp.Exam = av.Exam.ToResponse()
	}

	// 转换视频关联数据
	if len(av.Videos) > 0 {
		resp.Videos = make([]AssessmentVideoRelationResponse, len(av.Videos))
		for i, avr := range av.Videos {
			resp.Videos[i] = AssessmentVideoRelationResponse{
				ID:                avr.ID,
				AssessmentVideoID: avr.AssessmentVideoID,
				VideoID:           avr.VideoID,
				Sort:              avr.Sort,
				CreatedAt:         avr.CreatedAt,
				UpdatedAt:         avr.UpdatedAt,
			}
			if avr.Video != nil {
				resp.Videos[i].Video = avr.Video.ToResponse()
			}
		}
	}

	// 转换教师关联数据
	if len(av.Teachers) > 0 {
		resp.Teachers = make([]AssessmentVideoTeacherResponse, len(av.Teachers))
		for i, avt := range av.Teachers {
			resp.Teachers[i] = AssessmentVideoTeacherResponse{
				ID:                avt.ID,
				AssessmentVideoID: avt.AssessmentVideoID,
				TeacherID:         avt.TeacherID,
				CreatedAt:         avt.CreatedAt,
				UpdatedAt:         avt.UpdatedAt,
			}
			if avt.Teacher != nil {
				resp.Teachers[i].Teacher = avt.Teacher.ToResponse()
			}
		}
	}

	// 转换刷题试卷关联数据
	if len(av.PracticeExams) > 0 {
		resp.PracticeExams = make([]AssessmentVideoPracticeResponse, len(av.PracticeExams))
		for i, avp := range av.PracticeExams {
			resp.PracticeExams[i] = AssessmentVideoPracticeResponse{
				ID:                avp.ID,
				AssessmentVideoID: avp.AssessmentVideoID,
				ExamID:            avp.ExamID,
				CreatedAt:         avp.CreatedAt,
				UpdatedAt:         avp.UpdatedAt,
			}
			if avp.Exam != nil {
				resp.PracticeExams[i].Exam = avp.Exam.ToResponse()
			}
		}
	}

	return resp
}

// ToResponse 转换为响应格式
func (avr *AssessmentVideoRelation) ToResponse() *AssessmentVideoRelationResponse {
	resp := &AssessmentVideoRelationResponse{
		ID:                avr.ID,
		AssessmentVideoID: avr.AssessmentVideoID,
		VideoID:           avr.VideoID,
		Sort:              avr.Sort,
		CreatedAt:         avr.CreatedAt,
		UpdatedAt:         avr.UpdatedAt,
	}

	if avr.Video != nil {
		resp.Video = avr.Video.ToResponse()
	}

	return resp
}

// AssessmentVideoPractice 考核视频与刷题试卷关联模型
type AssessmentVideoPractice struct {
	ID                uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	AssessmentVideoID uint           `json:"assessment_video_id" gorm:"type:int;not null;comment:考核视频ID"`
	ExamID            uint           `json:"exam_id" gorm:"type:int;not null;comment:刷题试卷ID"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	AssessmentVideo *AssessmentVideo `json:"assessment_video,omitempty" gorm:"foreignKey:AssessmentVideoID"`
	Exam            *Exam            `json:"exam,omitempty" gorm:"foreignKey:ExamID"`
}

// AssessmentVideoPracticeResponse 考核视频与刷题试卷关联响应数据
type AssessmentVideoPracticeResponse struct {
	ID                uint          `json:"id"`
	AssessmentVideoID uint          `json:"assessment_video_id"`
	ExamID            uint          `json:"exam_id"`
	Exam              *ExamResponse `json:"exam,omitempty"`
	CreatedAt         time.Time     `json:"created_at"`
	UpdatedAt         time.Time     `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (avp *AssessmentVideoPractice) ToResponse() *AssessmentVideoPracticeResponse {
	resp := &AssessmentVideoPracticeResponse{
		ID:                avp.ID,
		AssessmentVideoID: avp.AssessmentVideoID,
		ExamID:            avp.ExamID,
		CreatedAt:         avp.CreatedAt,
		UpdatedAt:         avp.UpdatedAt,
	}

	if avp.Exam != nil {
		resp.Exam = avp.Exam.ToResponse()
	}

	return resp
}
