package routes

import (
	controllers "ai_select_admin/controllers"
	"ai_select_admin/middleware"
	"ai_select_admin/routes/h5"

	"github.com/gin-gonic/gin"
)

var r *gin.Engine

func init() {
	r = gin.New()
}
func SetupRoutes() *gin.Engine {
	// 添加中间件
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.RequestBodyLoggerMiddleware())
	r.Use(middleware.ErrorLoggerMiddleware())
	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})
	h5.InitH5Router(r)
	// 创建控制器实例
	adminController := &controllers.AdminController{}
	categoryController := &controllers.CategoryController{}
	videoController := &controllers.VideoController{}
	uploadController := controllers.NewUploadController()
	tencentAuthController := controllers.NewTencentAuthController()
	tencentNotifyController := controllers.NewTencentNotifyController()
	sseController := controllers.NewSSEController()
	questionCategoryController := &controllers.QuestionCategoryController{}
	questionController := &controllers.QuestionController{}
	examController := &controllers.ExamController{}
	assessmentCategoryController := &controllers.AssessmentCategoryController{}
	assessmentVideoController := &controllers.AssessmentVideoController{}
	wechatConfigController := &controllers.WechatConfigController{}
	departmentController := &controllers.DepartmentController{}
	employeeController := &controllers.EmployeeController{}
	assessmentRecordController := &controllers.AssessmentRecordController{}
	teacherController := &controllers.TeacherController{}

	r.GET("/WW_verify_CVLb6fsSIMllA7Mb.txt", func(c *gin.Context) {
		c.File("./static/WW_verify_CVLb6fsSIMllA7Mb.txt")
	})
	// API路由组
	api := r.Group("/api")
	{
		// 公开路由
		public := api.Group("/public")
		{
			public.POST("/admin/login", adminController.Login)
			public.GET("/upload/credentials", uploadController.GetUploadCredentials)
			public.GET("/upload/cos-url", uploadController.GetCOSURL)
			// SSE相关路由
			public.GET("/sse/video-status", sseController.VideoStatusStream)
			public.GET("/sse/status", sseController.GetSSEStatus)
			public.POST("/sse/test", sseController.BroadcastTestMessage)

		}

		// 腾讯云通知路由（无需认证）
		tencent := api.Group("/tencent")
		{
			tencent.POST("/videonotify", tencentNotifyController.VideoNotify)
		}

		// 需要认证的路由
		auth := api.Group("/auth")
		auth.Use(middleware.AuthMiddleware())
		{
			// 管理员个人信息
			auth.GET("/admin/profile", adminController.GetProfile)
			auth.GET("/get_upload_vedio_signature", tencentAuthController.GetSignature)
		}

		// 需要管理员权限的路由
		admin := api.Group("/admin")
		admin.Use(middleware.AdminAuthMiddleware())
		{
			// 管理员管理
			adminGroup := admin.Group("/admins")
			{
				adminGroup.GET("", adminController.ListAdmins)
				adminGroup.POST("", adminController.CreateAdmin)
				adminGroup.PUT("/:id", adminController.UpdateAdmin)
				adminGroup.DELETE("/:id", adminController.DeleteAdmin)
			}

			// 课程分类管理
			categoryGroup := admin.Group("/categories")
			{
				categoryGroup.GET("", categoryController.ListCategories)
				categoryGroup.GET("/tree", categoryController.GetCategoryTree)
				categoryGroup.GET("/:id", categoryController.GetCategory)
				categoryGroup.POST("", categoryController.CreateCategory)
				categoryGroup.PUT("/:id", categoryController.UpdateCategory)
				categoryGroup.DELETE("/:id", categoryController.DeleteCategory)
			}

			// 视频管理
			videoGroup := admin.Group("/videos")
			{
				videoGroup.GET("", videoController.ListVideos)
				videoGroup.GET("/:id", videoController.GetVideo)
				videoGroup.POST("", videoController.CreateVideo)
				videoGroup.PUT("/:id", videoController.UpdateVideo)
				videoGroup.DELETE("/:id", videoController.DeleteVideo)
			}

			// 题库分类管理
			questionCategoryGroup := admin.Group("/question-categories")
			{
				questionCategoryGroup.GET("", questionCategoryController.ListQuestionCategories)
				questionCategoryGroup.GET("/tree", questionCategoryController.GetQuestionCategoryTree)
				questionCategoryGroup.GET("/:id", questionCategoryController.GetQuestionCategory)
				questionCategoryGroup.POST("", questionCategoryController.CreateQuestionCategory)
				questionCategoryGroup.PUT("/:id", questionCategoryController.UpdateQuestionCategory)
				questionCategoryGroup.DELETE("/:id", questionCategoryController.DeleteQuestionCategory)
			}

			// 题目管理
			questionGroup := admin.Group("/questions")
			{
				questionGroup.GET("", questionController.ListQuestions)
				questionGroup.GET("/:id", questionController.GetQuestion)
				questionGroup.POST("", questionController.CreateQuestion)
				questionGroup.PUT("/:id", questionController.UpdateQuestion)
				questionGroup.DELETE("/:id", questionController.DeleteQuestion)
			}

			// 试卷管理
			examGroup := admin.Group("/exams")
			{
				examGroup.GET("", examController.ListExams)
				examGroup.GET("/:id", examController.GetExam)
				examGroup.POST("", examController.CreateExam)
				examGroup.PUT("/:id", examController.UpdateExam)
				examGroup.DELETE("/:id", examController.DeleteExam)
				examGroup.PUT("/:id/questions", examController.UpdateExamQuestions)
			}

			// 考核分类管理
			assessmentCategoryGroup := admin.Group("/assessment-categories")
			{
				assessmentCategoryGroup.GET("", assessmentCategoryController.ListAssessmentCategories)
				assessmentCategoryGroup.GET("/tree", assessmentCategoryController.GetAssessmentCategoryTree)
				assessmentCategoryGroup.GET("/:id", assessmentCategoryController.GetAssessmentCategory)
				assessmentCategoryGroup.POST("", assessmentCategoryController.CreateAssessmentCategory)
				assessmentCategoryGroup.PUT("/:id", assessmentCategoryController.UpdateAssessmentCategory)
				assessmentCategoryGroup.DELETE("/:id", assessmentCategoryController.DeleteAssessmentCategory)
			}

			// 考核视频管理
			assessmentVideoGroup := admin.Group("/assessment-videos")
			{
				assessmentVideoGroup.GET("", assessmentVideoController.ListAssessmentVideos)
				assessmentVideoGroup.GET("/search", assessmentVideoController.SearchAssessmentVideos)
				assessmentVideoGroup.GET("/:id", assessmentVideoController.GetAssessmentVideo)
				assessmentVideoGroup.POST("", assessmentVideoController.CreateAssessmentVideo)
				assessmentVideoGroup.PUT("/:id", assessmentVideoController.UpdateAssessmentVideo)
				assessmentVideoGroup.DELETE("/:id", assessmentVideoController.DeleteAssessmentVideo)
			}

			// 企业微信配置管理
			wechatConfigGroup := admin.Group("/wechat-configs")
			{
				wechatConfigGroup.GET("", wechatConfigController.ListWechatConfigs)
				wechatConfigGroup.GET("/tree", wechatConfigController.GetWechatConfigTree)
				wechatConfigGroup.GET("/:id", wechatConfigController.GetWechatConfig)
				wechatConfigGroup.POST("", wechatConfigController.CreateWechatConfig)
				wechatConfigGroup.PUT("/:id", wechatConfigController.UpdateWechatConfig)
				wechatConfigGroup.DELETE("/:id", wechatConfigController.DeleteWechatConfig)
			}

			// 部门管理
			departmentGroup := admin.Group("/departments")
			{
				departmentGroup.GET("", departmentController.ListDepartments)
				departmentGroup.GET("/tree", departmentController.GetDepartmentTree)
				departmentGroup.GET("/:id", departmentController.GetDepartment)
				departmentGroup.POST("/sync", departmentController.SyncDepartments)
			}

			// 员工管理
			employeeGroup := admin.Group("/employees")
			{
				employeeGroup.GET("", employeeController.ListEmployees)
				employeeGroup.GET("/search", employeeController.SearchEmployees)
				employeeGroup.GET("/:id", employeeController.GetEmployee)
				employeeGroup.POST("/sync", employeeController.SyncEmployees)
				employeeGroup.POST("/sync-all", employeeController.SyncAll)
			}

			// 考核记录管理
			assessmentRecordGroup := admin.Group("/assessment-records")
			{
				assessmentRecordGroup.GET("", assessmentRecordController.ListAssessmentRecords)
				assessmentRecordGroup.GET("/:id", assessmentRecordController.GetAssessmentRecord)
				assessmentRecordGroup.POST("", assessmentRecordController.CreateAssessmentRecord)
				assessmentRecordGroup.PUT("/:id", assessmentRecordController.UpdateAssessmentRecord)
				assessmentRecordGroup.DELETE("/:id", assessmentRecordController.DeleteAssessmentRecord)
				assessmentRecordGroup.GET("/exam-detail/:id", assessmentRecordController.GetExamDetail)
				assessmentRecordGroup.POST("/fix-status", assessmentRecordController.FixAssessmentRecordStatus) // 临时修复接口
			}

			// 教师管理
			teacherGroup := admin.Group("/teachers")
			{
				teacherGroup.GET("", teacherController.ListTeachers)
				teacherGroup.GET("/search", teacherController.SearchTeachers)
				teacherGroup.GET("/:id", teacherController.GetTeacher)
				teacherGroup.POST("", teacherController.CreateTeacher)
				teacherGroup.PUT("/:id", teacherController.UpdateTeacher)
				teacherGroup.DELETE("/:id", teacherController.DeleteTeacher)
			}
		}
	}

	// 静态文件服务
	r.Static("/uploads", "./uploads")

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "AI Select Admin API is running",
		})
	})

	return r
}
