package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type WechatConfigController struct{}

// ListWechatConfigs 获取企业微信配置列表
func (wcc *WechatConfigController) ListWechatConfigs(c *gin.Context) {
	var req models.WechatConfigListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.WechatConfig{})

	// 按名称搜索
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取企业微信配置总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var configs []models.WechatConfig
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&configs).Error; err != nil {
		logger.Errorf("获取企业微信配置列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var responses []*models.WechatConfigResponse
	for _, config := range configs {
		responses = append(responses, config.ToResponse())
	}

	// 计算分页信息
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	utils.PageSuccess(c, responses, utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Total:   total,
		Pages:   totalPages,
	})
}

// GetWechatConfig 获取企业微信配置详情
func (wcc *WechatConfigController) GetWechatConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的ID")
		return
	}

	var config models.WechatConfig
	if err := database.DB.First(&config, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "企业微信配置不存在")
		} else {
			logger.Errorf("获取企业微信配置失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, config.ToResponse())
}

// CreateWechatConfig 创建企业微信配置
func (wcc *WechatConfigController) CreateWechatConfig(c *gin.Context) {
	var req models.WechatConfigCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 创建企业微信配置
	config := models.WechatConfig{
		CorpID:      req.CorpID,
		AgentID:     req.AgentID,
		Secret:      req.Secret,
		Name:        req.Name,
		Description: req.Description,
		Status:      req.Status,
	}

	// 设置默认状态
	if config.Status == 0 {
		config.Status = 1
	}

	if err := database.DB.Create(&config).Error; err != nil {
		logger.Errorf("创建企业微信配置失败: %v", err)
		utils.InternalServerError(c, "创建失败")
		return
	}

	utils.Success(c, config.ToResponse())
}

// UpdateWechatConfig 更新企业微信配置
func (wcc *WechatConfigController) UpdateWechatConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的ID")
		return
	}

	var req models.WechatConfigUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查找企业微信配置
	var config models.WechatConfig
	if err := database.DB.First(&config, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "企业微信配置不存在")
		} else {
			logger.Errorf("查询企业微信配置失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 更新字段
	config.CorpID = req.CorpID
	config.AgentID = req.AgentID
	config.Secret = req.Secret
	config.Name = req.Name
	config.Description = req.Description
	config.Status = req.Status

	if err := database.DB.Save(&config).Error; err != nil {
		logger.Errorf("更新企业微信配置失败: %v", err)
		utils.InternalServerError(c, "更新失败")
		return
	}

	utils.Success(c, config.ToResponse())
}

// DeleteWechatConfig 删除企业微信配置
func (wcc *WechatConfigController) DeleteWechatConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的ID")
		return
	}

	// 查找企业微信配置
	var config models.WechatConfig
	if err := database.DB.First(&config, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "企业微信配置不存在")
		} else {
			logger.Errorf("查询企业微信配置失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	if err := database.DB.Delete(&config).Error; err != nil {
		logger.Errorf("删除企业微信配置失败: %v", err)
		utils.InternalServerError(c, "删除失败")
		return
	}

	utils.Success(c, "删除成功")
}

// GetWechatConfigTree 获取企业微信配置树形结构
func (wcc *WechatConfigController) GetWechatConfigTree(c *gin.Context) {
	var configs []models.WechatConfig
	if err := database.DB.Where("status = ?", 1).Order("created_at ASC").Find(&configs).Error; err != nil {
		logger.Errorf("获取企业微信配置树失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	var responses []*models.WechatConfigResponse
	for _, config := range configs {
		responses = append(responses, config.ToResponse())
	}

	utils.Success(c, responses)
}
