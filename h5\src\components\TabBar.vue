<template>
  <van-tabbar v-model="active" @change="onChange">
    <van-tabbar-item icon="home-o" to="/course">课程</van-tabbar-item>
    <van-tabbar-item icon="edit" to="/exam">考试</van-tabbar-item>
    <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
  </van-tabbar>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();
const active = ref(0);

// 根据当前路由设置激活的tab
const setActiveTab = () => {
  if (route.path === "/course") {
    active.value = 0;
  } else if (route.path === "/exam") {
    active.value = 1;
  } else if (route.path === "/profile") {
    active.value = 2;
  }
};

// 监听路由变化
router.afterEach(() => {
  setActiveTab();
});

// 初始化
setActiveTab();

const onChange = (index: number) => {
  active.value = index;
};
</script>

<style scoped>
:deep(.van-tabbar) {
  background-color: #fff;
  border-top: 1px solid #ebedf0;
}

:deep(.van-tabbar-item--active) {
  color: #1bb394;
}

:deep(.van-tabbar-item__icon) {
  font-size: 22px;
}

:deep(.van-tabbar-item__text) {
  font-size: 12px;
  margin-top: 4px;
}
</style>
