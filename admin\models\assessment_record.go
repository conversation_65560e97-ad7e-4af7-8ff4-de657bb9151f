package models

import (
	"time"

	"gorm.io/gorm"
)

// AssessmentRecordStatus 考核记录状态
type AssessmentRecordStatus int

const (
	AssessmentRecordStatusNotStarted AssessmentRecordStatus = 0 // 未开始
	AssessmentRecordStatusInProgress AssessmentRecordStatus = 1 // 进行中
	AssessmentRecordStatusCompleted  AssessmentRecordStatus = 2 // 已完成
	AssessmentRecordStatusExpired    AssessmentRecordStatus = 3 // 已过期
)

// AssessmentRecord 考核记录模型
type AssessmentRecord struct {
	ID                uint                   `json:"id" gorm:"primaryKey;autoIncrement"`
	EmployeeID        uint                   `json:"employee_id" gorm:"type:int;not null;comment:员工ID"`
	AssessmentVideoID uint                   `json:"assessment_video_id" gorm:"type:int;not null;comment:考核视频ID"`
	Status            AssessmentRecordStatus `json:"status" gorm:"type:tinyint(1);default:0;comment:状态 0未开始 1进行中 2已完成 3已过期"`
	AssignedAt        time.Time              `json:"assigned_at" gorm:"type:datetime;comment:分配时间"`
	StartedAt         *time.Time             `json:"started_at" gorm:"type:datetime;comment:开始时间"`
	CompletedAt       *time.Time             `json:"completed_at" gorm:"type:datetime;comment:完成时间"`
	ExamStartTime     *time.Time             `json:"exam_start_time" gorm:"type:datetime;comment:考试开始时间"`
	Score             *int                   `json:"score" gorm:"type:int;comment:得分"`
	VideoProgress     int                    `json:"video_progress" gorm:"type:int;default:0;comment:视频观看进度(百分比)"`
	ExamScore         *int                   `json:"exam_score" gorm:"type:int;comment:考试得分"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
	DeletedAt         gorm.DeletedAt         `json:"-" gorm:"index"`

	// 关联
	Employee        *Employee        `json:"employee,omitempty" gorm:"foreignKey:EmployeeID"`
	AssessmentVideo *AssessmentVideo `json:"assessment_video,omitempty" gorm:"foreignKey:AssessmentVideoID"`
}

// AssessmentRecordListRequest 考核记录列表请求
type AssessmentRecordListRequest struct {
	Page                 int    `form:"page" binding:"omitempty,min=1"`
	PageSize             int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	EmployeeName         string `form:"employee_name"`          // 员工姓名搜索
	AssessmentVideoTitle string `form:"assessment_video_title"` // 考核视频标题搜索
	DepartmentID         *int   `form:"department_id"`          // 部门ID筛选
	Status               *int   `form:"status"`                 // 状态筛选
	EmployeeID           *uint  `form:"employee_id"`            // 员工ID筛选
	AssessmentVideoID    *uint  `form:"assessment_video_id"`    // 考核视频ID筛选
	IsPassed             *int   `form:"is_passed"`              // 是否及格筛选 0不及格 1及格 2未考试
}

// AssessmentRecordCreateRequest 创建考核记录请求
type AssessmentRecordCreateRequest struct {
	EmployeeIDs       []uint  `json:"employee_ids" binding:"required,min=1"`
	AssessmentVideoID uint    `json:"assessment_video_id" binding:"required"`
	ExamStartTime     *string `json:"exam_start_time"` // 考试开始时间（可选，字符串格式）
}

// AssessmentRecordUpdateRequest 更新考核记录请求
type AssessmentRecordUpdateRequest struct {
	Status        *AssessmentRecordStatus `json:"status"`
	VideoProgress *int                    `json:"video_progress"`
	ExamScore     *int                    `json:"exam_score"`
	ExamStartTime *string                 `json:"exam_start_time"` // 考试开始时间（字符串格式）
}

// AssessmentRecordResponse 考核记录响应数据
type AssessmentRecordResponse struct {
	ID                uint                     `json:"id"`
	EmployeeID        uint                     `json:"employee_id"`
	AssessmentVideoID uint                     `json:"assessment_video_id"`
	Status            AssessmentRecordStatus   `json:"status"`
	StatusText        string                   `json:"status_text"`
	AssignedAt        time.Time                `json:"assigned_at"`
	StartedAt         *time.Time               `json:"started_at"`
	CompletedAt       *time.Time               `json:"completed_at"`
	ExamStartTime     *time.Time               `json:"exam_start_time"`
	Score             *int                     `json:"score"`
	VideoProgress     int                      `json:"video_progress"`
	ExamScore         *int                     `json:"exam_score"`
	ExamStatus        int                      `json:"exam_status"`
	ExamStatusText    string                   `json:"exam_status_text"`
	PracticeCount     int                      `json:"practice_count"`
	IsPassed          *bool                    `json:"is_passed"`      // 是否及格
	PassScore         *int                     `json:"pass_score"`     // 及格分
	IsPassedText      string                   `json:"is_passed_text"` // 是否及格文本
	Employee          *EmployeeResponse        `json:"employee,omitempty"`
	AssessmentVideo   *AssessmentVideoResponse `json:"assessment_video,omitempty"`
	CreatedAt         time.Time                `json:"created_at"`
	UpdatedAt         time.Time                `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (ar *AssessmentRecord) ToResponse() *AssessmentRecordResponse {
	statusText := ""
	switch ar.Status {
	case AssessmentRecordStatusNotStarted:
		statusText = "未开始"
	case AssessmentRecordStatusInProgress:
		statusText = "进行中"
	case AssessmentRecordStatusCompleted:
		statusText = "已完成"
	case AssessmentRecordStatusExpired:
		statusText = "已过期"
	}

	// 根据考试得分判断考试状态
	examStatusText := "未考试"
	examStatus := 0
	if ar.ExamScore != nil {
		examStatusText = "已考试"
		examStatus = 1
	}

	// 计算是否及格状态
	var isPassed *bool
	var passScore *int
	isPassedText := "未考试"

	if ar.AssessmentVideo != nil && ar.AssessmentVideo.Exam != nil {
		passScore = &ar.AssessmentVideo.Exam.PassScore
		if ar.ExamScore != nil {
			passed := *ar.ExamScore >= ar.AssessmentVideo.Exam.PassScore
			isPassed = &passed
			if passed {
				isPassedText = "及格"
			} else {
				isPassedText = "不及格"
			}
		}
	}

	resp := &AssessmentRecordResponse{
		ID:                ar.ID,
		EmployeeID:        ar.EmployeeID,
		AssessmentVideoID: ar.AssessmentVideoID,
		Status:            ar.Status,
		StatusText:        statusText,
		AssignedAt:        ar.AssignedAt,
		StartedAt:         ar.StartedAt,
		CompletedAt:       ar.CompletedAt,
		ExamStartTime:     ar.ExamStartTime,
		Score:             ar.Score,
		VideoProgress:     ar.VideoProgress,
		ExamScore:         ar.ExamScore,
		ExamStatus:        examStatus,
		ExamStatusText:    examStatusText,
		IsPassed:          isPassed,
		PassScore:         passScore,
		IsPassedText:      isPassedText,
		CreatedAt:         ar.CreatedAt,
		UpdatedAt:         ar.UpdatedAt,
	}

	if ar.Employee != nil {
		resp.Employee = ar.Employee.ToResponse()
	}

	if ar.AssessmentVideo != nil {
		resp.AssessmentVideo = ar.AssessmentVideo.ToResponse()
	}

	return resp
}
