import request from '@/utils/request'
import type { 
  QuestionCategory, 
  QuestionCategoryListRequest, 
  QuestionCategoryCreateRequest, 
  QuestionCategoryUpdateRequest 
} from '@/types/questionCategory'

// 获取题库分类列表
export function getQuestionCategoryList(params: QuestionCategoryListRequest) {
  return request({
    url: '/admin/question-categories',
    method: 'get',
    params
  })
}

// 获取题库分类树
export function getQuestionCategoryTree() {
  return request({
    url: '/admin/question-categories/tree',
    method: 'get'
  })
}

// 获取题库分类详情
export function getQuestionCategory(id: number) {
  return request({
    url: `/admin/question-categories/${id}`,
    method: 'get'
  })
}

// 创建题库分类
export function createQuestionCategory(data: QuestionCategoryCreateRequest) {
  return request({
    url: '/admin/question-categories',
    method: 'post',
    data
  })
}

// 更新题库分类
export function updateQuestionCategory(id: number, data: QuestionCategoryUpdateRequest) {
  return request({
    url: `/admin/question-categories/${id}`,
    method: 'put',
    data
  })
}

// 删除题库分类
export function deleteQuestionCategory(id: number) {
  return request({
    url: `/admin/question-categories/${id}`,
    method: 'delete'
  })
}
