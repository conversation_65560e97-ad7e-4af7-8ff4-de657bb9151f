package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/services"
	"ai_select_admin/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type EmployeeController struct{}

// ListEmployees 获取员工列表
func (ec *EmployeeController) ListEmployees(c *gin.Context) {
	var req models.EmployeeListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.Employee{}).Preload("Departments")

	// 按姓名搜索
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 按部门筛选
	if req.DepartmentID != nil {
		query = query.Where("JSON_CONTAINS(department, ?)", strconv.Itoa(*req.DepartmentID))
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取员工总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var employees []models.Employee
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&employees).Error; err != nil {
		logger.Errorf("获取员工列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var responses []*models.EmployeeResponse
	for _, employee := range employees {
		responses = append(responses, employee.ToResponse())
	}

	// 计算分页信息
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))
	page := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Pages:   totalPages,
		Total:   total,
	}
	utils.PageSuccess(c, responses, page)
}

// GetEmployee 获取员工详情
func (ec *EmployeeController) GetEmployee(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的ID")
		return
	}

	var employee models.Employee
	if err := database.DB.Preload("Departments").First(&employee, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "员工不存在")
		} else {
			logger.Errorf("获取员工失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, employee.ToResponse())
}

// SearchEmployees 搜索员工（用于下拉选择）
func (ec *EmployeeController) SearchEmployees(c *gin.Context) {
	var req models.EmployeeSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 构建查询条件
	query := database.DB.Model(&models.Employee{}).Preload("Departments")

	// 按姓名搜索
	if req.Keyword != "" {
		query = query.Where("name LIKE ?", "%"+req.Keyword+"%")
	}

	// 按部门筛选
	if req.DepartmentID != nil {
		query = query.Where("JSON_CONTAINS(department, ?)", strconv.Itoa(*req.DepartmentID))
	}

	// 按状态筛选（默认只查询正常状态的员工）
	query = query.Where("status = ?", 1)

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取员工总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var employees []models.Employee
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("name ASC").Find(&employees).Error; err != nil {
		logger.Errorf("搜索员工失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var responses []*models.EmployeeResponse
	for _, employee := range employees {
		responses = append(responses, employee.ToResponse())
	}

	// 计算分页信息
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))
	page := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Pages:   totalPages,
		Total:   total,
	}
	utils.PageSuccess(c, responses, page)
}

// SyncEmployees 同步企业微信员工数据
func (ec *EmployeeController) SyncEmployees(c *gin.Context) {
	// 获取默认企业微信配置
	config, err := services.GetDefaultWechatConfig()
	if err != nil {
		utils.BadRequest(c, "未找到可用的企业微信配置")
		return
	}

	// 创建企业微信服务
	wechatService := services.NewWechatService(config)

	// 同步员工数据
	if err := wechatService.SyncEmployees(); err != nil {
		logger.Errorf("同步员工数据失败: %v", err)
		utils.InternalServerError(c, "同步失败: "+err.Error())
		return
	}

	utils.Success(c, "同步成功")
}

// SyncAll 同步所有企业微信数据（部门+员工）
func (ec *EmployeeController) SyncAll(c *gin.Context) {
	// 获取默认企业微信配置
	config, err := services.GetDefaultWechatConfig()
	if err != nil {
		utils.BadRequest(c, "未找到可用的企业微信配置")
		return
	}

	// 创建企业微信服务
	wechatService := services.NewWechatService(config)

	// 先同步部门数据
	if err := wechatService.SyncDepartments(); err != nil {
		logger.Errorf("同步部门数据失败: %v", err)
		utils.InternalServerError(c, "同步部门失败: "+err.Error())
		return
	}

	// 再同步员工数据
	if err := wechatService.SyncEmployees(); err != nil {
		logger.Errorf("同步员工数据失败: %v", err)
		utils.InternalServerError(c, "同步员工失败: "+err.Error())
		return
	}

	utils.Success(c, "同步成功")
}
