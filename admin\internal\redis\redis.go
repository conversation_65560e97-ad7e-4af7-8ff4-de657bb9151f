package redis

import (
	"ai_select_admin/config"
	"ai_select_admin/logger"
	"context"

	"github.com/redis/go-redis/v9"
)

var (
	Rdb *redis.Client
	Ctx = context.Background()
)

func InitRedis() error {
	Rdb = redis.NewClient(&redis.Options{
		Addr:     config.AppConfig.Redis.Host + ":" + config.AppConfig.Redis.Port,
		Password: "", // no password set
		DB:       0,  // use default DB
	})
	if _, err := Rdb.Ping(Ctx).Result(); err != nil {
		logger.Errorf("初始化redis 失败%s\n", err.Error())
		return err

	}
	logger.Info("redis 连接成功")
	return nil

}
