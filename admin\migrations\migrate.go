package main

import (
	"ai_select_admin/config"
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"fmt"
	"log"
)

func main() {
	// 初始化配置
	config.Init()

	// 初始化日志
	logger.Init()

	// 初始化数据库
	if err := database.Init(); err != nil {
		log.Fatal("数据库初始化失败:", err)
	}

	// 执行迁移
	if err := migrateDepartmentOrderToSort(); err != nil {
		log.Fatal("迁移失败:", err)
	}

	fmt.Println("迁移完成！")
}

func migrateDepartmentOrderToSort() error {
	// 检查是否已经存在sort列
	var count int64
	err := database.DB.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = ? AND table_name = ? AND column_name = 'sort'",
		config.AppConfig.Database.Database, config.AppConfig.Database.Prefix+"department").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("检查sort列是否存在失败: %v", err)
	}

	if count > 0 {
		fmt.Println("sort列已存在，跳过迁移")
		return nil
	}

	// 检查是否存在order列
	err = database.DB.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = ? AND table_name = ? AND column_name = 'order'",
		config.AppConfig.Database.Database, config.AppConfig.Database.Prefix+"department").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("检查order列是否存在失败: %v", err)
	}

	if count == 0 {
		fmt.Println("order列不存在，跳过迁移")
		return nil
	}

	// 执行重命名
	tableName := config.AppConfig.Database.Prefix + "department"
	sql := fmt.Sprintf("ALTER TABLE %s RENAME COLUMN `order` TO `sort`", tableName)
	
	fmt.Printf("执行SQL: %s\n", sql)
	if err := database.DB.Exec(sql).Error; err != nil {
		return fmt.Errorf("重命名列失败: %v", err)
	}

	// 更新列注释
	sql = fmt.Sprintf("ALTER TABLE %s MODIFY COLUMN `sort` int DEFAULT 0 COMMENT '在父部门中的次序值'", tableName)
	fmt.Printf("执行SQL: %s\n", sql)
	if err := database.DB.Exec(sql).Error; err != nil {
		return fmt.Errorf("更新列注释失败: %v", err)
	}

	fmt.Println("部门表order字段已成功重命名为sort字段")
	return nil
}
