package main

import (
	"ai_select_admin/models"
	"fmt"
	"log"
	"math/rand"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// 数据库连接配置
const dsn = "root:111111@tcp(127.0.0.1:3306)/hr_course?charset=utf8mb4&parseTime=True&loc=Local"

var db *gorm.DB

func init() {
	rand.Seed(time.Now().UnixNano())
}

func main() {
	// 连接数据库
	var err error
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "hr_",
			SingularTable: true,
		},
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("开始生成测试数据...")

	// 1. 创建题库分类
	categories := createQuestionCategories()
	fmt.Printf("创建了 %d 个题库分类\n", len(categories))

	// 2. 创建题目
	questions := createQuestions(categories)
	fmt.Printf("创建了 %d 个题目\n", len(questions))

	// 3. 创建考试试卷
	examPapers := createExamPapers(questions)
	fmt.Printf("创建了 %d 个考试试卷\n", len(examPapers))

	// 4. 创建刷题试卷
	practiceExams := createPracticeExams(questions)
	fmt.Printf("创建了 %d 个刷题试卷\n", len(practiceExams))

	fmt.Println("测试数据生成完成！")
}

// 创建题库分类
func createQuestionCategories() []models.QuestionCategory {
	categories := []models.QuestionCategory{
		{Name: "计算机基础", Description: "计算机基础知识题目", Sort: 1, Status: 1},
		{Name: "编程语言", Description: "各种编程语言相关题目", Sort: 2, Status: 1},
		{Name: "数据库", Description: "数据库相关知识题目", Sort: 3, Status: 1},
		{Name: "网络技术", Description: "网络技术相关题目", Sort: 4, Status: 1},
		{Name: "系统架构", Description: "系统架构设计题目", Sort: 5, Status: 1},
		{Name: "项目管理", Description: "项目管理相关题目", Sort: 6, Status: 1},
		{Name: "软件测试", Description: "软件测试相关题目", Sort: 7, Status: 1},
		{Name: "安全技术", Description: "信息安全相关题目", Sort: 8, Status: 1},
	}

	for i := range categories {
		if err := db.Create(&categories[i]).Error; err != nil {
			log.Printf("创建题库分类失败: %v", err)
		}
	}

	return categories
}

// 创建题目
func createQuestions(categories []models.QuestionCategory) []models.Question {
	var questions []models.Question

	// 题目模板
	questionTemplates := []struct {
		title      string
		content    string
		options    []models.QuestionOption
		answer     string
		difficulty models.QuestionDifficulty
	}{
		{
			title:   "什么是HTTP协议？",
			content: "HTTP（HyperText Transfer Protocol）是什么类型的协议？",
			options: []models.QuestionOption{
				{Label: "A", Content: "应用层协议"},
				{Label: "B", Content: "传输层协议"},
				{Label: "C", Content: "网络层协议"},
				{Label: "D", Content: "数据链路层协议"},
			},
			answer:     "A",
			difficulty: models.DifficultyEasy,
		},
		{
			title:   "数据库事务的ACID特性",
			content: "数据库事务的ACID特性中，A代表什么？",
			options: []models.QuestionOption{
				{Label: "A", Content: "原子性（Atomicity）"},
				{Label: "B", Content: "一致性（Consistency）"},
				{Label: "C", Content: "隔离性（Isolation）"},
				{Label: "D", Content: "持久性（Durability）"},
			},
			answer:     "A",
			difficulty: models.DifficultyMedium,
		},
		{
			title:   "Go语言的特点",
			content: "以下哪个不是Go语言的特点？",
			options: []models.QuestionOption{
				{Label: "A", Content: "静态类型"},
				{Label: "B", Content: "垃圾回收"},
				{Label: "C", Content: "面向对象"},
				{Label: "D", Content: "动态类型"},
			},
			answer:     "D",
			difficulty: models.DifficultyEasy,
		},
		{
			title:   "TCP和UDP的区别",
			content: "TCP协议相比UDP协议的主要特点是什么？",
			options: []models.QuestionOption{
				{Label: "A", Content: "无连接"},
				{Label: "B", Content: "可靠传输"},
				{Label: "C", Content: "速度更快"},
				{Label: "D", Content: "开销更小"},
			},
			answer:     "B",
			difficulty: models.DifficultyMedium,
		},
		{
			title:   "RESTful API设计原则",
			content: "RESTful API设计中，GET请求应该用于什么操作？",
			options: []models.QuestionOption{
				{Label: "A", Content: "创建资源"},
				{Label: "B", Content: "更新资源"},
				{Label: "C", Content: "删除资源"},
				{Label: "D", Content: "获取资源"},
			},
			answer:     "D",
			difficulty: models.DifficultyEasy,
		},
	}

	// 为每个分类创建题目
	for _, category := range categories {
		for i := 0; i < 10; i++ { // 每个分类创建10个题目
			template := questionTemplates[rand.Intn(len(questionTemplates))]

			question := models.Question{
				Title:         fmt.Sprintf("[%s] %s %d", category.Name, template.title, i+1),
				Content:       template.content,
				Type:          models.QuestionTypeChoice,
				OptionsData:   template.options,
				CorrectAnswer: template.answer,
				Difficulty:    template.difficulty,
				CategoryID:    category.ID,
				Status:        1,
			}

			if err := db.Create(&question).Error; err != nil {
				log.Printf("创建题目失败: %v", err)
				continue
			}

			questions = append(questions, question)
		}
	}

	return questions
}

// 创建考试试卷
func createExamPapers(questions []models.Question) []models.Exam {
	var exams []models.Exam

	examTemplates := []struct {
		title       string
		description string
		duration    int
		totalScore  int
		passScore   int
	}{
		{"计算机基础知识考试", "测试计算机基础知识掌握情况", 60, 100, 60},
		{"编程能力测试", "评估编程语言掌握程度", 90, 100, 70},
		{"数据库应用考试", "数据库设计和应用能力测试", 75, 100, 65},
		{"网络技术考试", "网络技术相关知识考试", 60, 100, 60},
		{"系统架构设计考试", "系统架构设计能力测试", 120, 100, 75},
	}

	for _, template := range examTemplates {
		exam := models.Exam{
			Title:       template.title,
			Description: template.description,
			Type:        1, // 考试试卷
			Duration:    template.duration,
			TotalScore:  template.totalScore,
			PassScore:   template.passScore,
			Status:      1,
		}

		if err := db.Create(&exam).Error; err != nil {
			log.Printf("创建考试试卷失败: %v", err)
			continue
		}

		// 为试卷添加题目（随机选择20个题目）
		selectedQuestions := selectRandomQuestions(questions, 20)
		for j, question := range selectedQuestions {
			examQuestion := models.ExamQuestion{
				ExamID:     exam.ID,
				QuestionID: question.ID,
				Score:      5, // 每题5分
				Sort:       j + 1,
			}

			if err := db.Create(&examQuestion).Error; err != nil {
				log.Printf("创建试卷题目关联失败: %v", err)
			}
		}

		exams = append(exams, exam)
	}

	return exams
}

// 创建刷题试卷
func createPracticeExams(questions []models.Question) []models.Exam {
	var exams []models.Exam

	practiceTemplates := []struct {
		title       string
		description string
		duration    int
		totalScore  int
		passScore   int
	}{
		{"计算机基础刷题", "计算机基础知识练习题集", 30, 50, 30},
		{"编程语言刷题", "编程语言基础练习", 45, 75, 45},
		{"数据库刷题", "数据库知识练习题集", 40, 60, 36},
		{"网络技术刷题", "网络技术练习题", 35, 55, 33},
		{"综合能力刷题", "综合技术能力练习", 60, 100, 60},
	}

	for _, template := range practiceTemplates {
		exam := models.Exam{
			Title:       template.title,
			Description: template.description,
			Type:        2, // 刷题试卷
			Duration:    template.duration,
			TotalScore:  template.totalScore,
			PassScore:   template.passScore,
			Status:      1,
		}

		if err := db.Create(&exam).Error; err != nil {
			log.Printf("创建刷题试卷失败: %v", err)
			continue
		}

		// 为刷题试卷添加题目（随机选择15个题目）
		selectedQuestions := selectRandomQuestions(questions, 15)
		scorePerQuestion := template.totalScore / len(selectedQuestions)

		for j, question := range selectedQuestions {
			examQuestion := models.ExamQuestion{
				ExamID:     exam.ID,
				QuestionID: question.ID,
				Score:      scorePerQuestion,
				Sort:       j + 1,
			}

			if err := db.Create(&examQuestion).Error; err != nil {
				log.Printf("创建试卷题目关联失败: %v", err)
			}
		}

		exams = append(exams, exam)
	}

	return exams
}

// 随机选择题目
func selectRandomQuestions(questions []models.Question, count int) []models.Question {
	if len(questions) <= count {
		return questions
	}

	selected := make([]models.Question, 0, count)
	used := make(map[int]bool)

	for len(selected) < count {
		index := rand.Intn(len(questions))
		if !used[index] {
			selected = append(selected, questions[index])
			used[index] = true
		}
	}

	return selected
}
