<template>
  <div class="assessment-record-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="所属部门">
          <el-tree-select
            v-model="searchForm.department_id"
            :data="departmentTree"
            :props="{ label: 'name', value: 'wechat_id' }"
            placeholder="请选择部门"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="员工姓名">
          <el-select
            v-model="searchForm.employee_id"
            placeholder="请选择员工"
            clearable
            filterable
            remote
            :remote-method="handleEmployeeSearch"
            :loading="employeeSearchLoading"
            style="width: 200px"
            @focus="handleEmployeeSearchFocus"
          >
            <el-option
              v-for="employee in employeeSearchOptions"
              :key="employee.id"
              :label="employee.name"
              :value="employee.id"
            >
              <div class="employee-option">
                <span class="employee-name">{{ employee.name }}</span>
                <span class="employee-position">{{ employee.position }}</span>
                <span
                  class="employee-department"
                  v-if="employee.departments && employee.departments.length > 0"
                >
                  {{ employee.departments.map((d) => d.name).join(", ") }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="考核视频">
          <el-select
            v-model="searchForm.assessment_video_id"
            placeholder="请选择考核视频"
            clearable
            filterable
            remote
            :remote-method="handleVideoSearch"
            :loading="videoSearchLoading"
            style="width: 200px"
            @focus="handleVideoSearchFocus"
          >
            <el-option
              v-for="video in videoSearchOptions"
              :key="video.id"
              :label="video.title"
              :value="video.id"
            >
              <div class="video-option">
                <span class="video-title">{{ video.title }}</span>
                <span class="video-category" v-if="video.category">
                  {{ video.category.name }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="未开始" :value="0" />
            <el-option label="进行中" :value="1" />
            <el-option label="已完成" :value="2" />
            <el-option label="已过期" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否及格">
          <el-select
            v-model="searchForm.is_passed"
            placeholder="请选择"
            clearable
            style="width: 120px"
          >
            <el-option label="及格" :value="1" />
            <el-option label="不及格" :value="0" />
            <el-option label="未考试" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleSearch"
            style="background-color: #1bb394; border-color: #1bb394"
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="action-card">
      <el-button
        type="primary"
        @click="handleShowCreateDialog"
        style="background-color: #1bb394; border-color: #1bb394"
      >
        <el-icon><Plus color="#fff" /></el-icon>
        创建考核
      </el-button>
      <el-button
        type="success"
        @click="handleBatchCreateAssessment"
        :disabled="uniqueSelectedEmployees.length === 0"
        style="background-color: #1bb394; border-color: #1bb394"
      >
        <el-icon><Plus color="#fff" /></el-icon>
        批量快捷创建 ({{ uniqueSelectedEmployees.length }})
      </el-button>
      <el-button
        @click="fetchAssessmentRecordList"
        style="background-color: #1bb394; border-color: #1bb394; color: white"
      >
        <el-icon><Refresh /></el-icon>
      </el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="employee.name"
          label="员工姓名"
          width="120"
          align="center"
        />
        <el-table-column
          prop="employee.position"
          label="职位"
          width="120"
          align="center"
        />
        <el-table-column
          prop="assessment_video.title"
          label="考核视频"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="assessment_video.category.name"
          label="考核分类"
          width="120"
          align="center"
        />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.exam_status)" size="small">
              {{ row.exam_status_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="video_progress"
          label="视频进度"
          width="100"
          align="center"
        >
          <template #default="{ row }"> {{ row.video_progress }}% </template>
        </el-table-column>
        <el-table-column
          prop="exam_score"
          label="考试得分"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <span v-if="row.exam_score !== null"> {{ row.exam_score }}分 </span>
            <span v-else class="text-gray-400">未考试</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="is_passed"
          label="是否及格"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              v-if="row.exam_score !== null && row.is_passed !== null"
              :type="row.is_passed ? 'success' : 'danger'"
              size="small"
            >
              {{ row.is_passed_text }}
            </el-tag>
            <span v-else class="text-gray-400">{{ row.is_passed_text }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考试详情" width="100" align="center">
          <template #default="{ row }">
            <el-button
              v-if="row.exam_score !== null && hasExamPaper(row)"
              type="primary"
              size="small"
              text
              @click="showExamDetail(row)"
            >
              查看详情
            </el-button>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column label="刷题数量" width="100" align="center">
          <template #default="{ row }">
            {{ row.practice_count || 0 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="exam_start_time"
          label="考试时间"
          width="180"
          align="center"
        >
          <template #default="{ row }">
            <span v-if="row.exam_start_time">
              {{ formatDateTime(row.exam_start_time) }}
            </span>
            <span v-else class="text-gray-400">未设置</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="assigned_at"
          label="分配时间"
          width="180"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.assigned_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              link
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              link
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchAssessmentRecordList"
          @current-change="fetchAssessmentRecordList"
        />
      </div>
    </el-card>

    <!-- 创建考核对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建考核"
      width="1000px"
      :close-on-click-modal="false"
    >
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="考核视频" required>
          <el-select
            v-model="createForm.assessment_video_id"
            placeholder="请选择考核视频"
            style="width: 100%"
            filterable
            remote
            :remote-method="handleCreateVideoSearch"
            :loading="createVideoSearchLoading"
            @focus="handleCreateVideoSearchFocus"
          >
            <el-option
              v-for="video in createVideoSearchOptions"
              :key="video.id"
              :label="video.title"
              :value="video.id"
            >
              <div class="video-option">
                <span class="video-title">{{ video.title }}</span>
                <span class="video-category" v-if="video.category">
                  {{ video.category.name }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="考试时间"
          v-if="
            selectedAssessmentVideo &&
            selectedAssessmentVideo.exam &&
            selectedAssessmentVideo.exam.type === 1
          "
        >
          <el-date-picker
            v-model="createForm.exam_start_time"
            type="datetime"
            placeholder="选择考试开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          <div class="form-tip">
            只有考试试卷需要设置考试时间，未到时间不允许参加考试
          </div>
        </el-form-item>
        <el-form-item label="选择员工" required>
          <div class="employee-selection-container">
            <!-- 左侧：搜索和选择 -->
            <div class="employee-search-panel">
              <div class="search-filters">
                <el-tree-select
                  v-model="createEmployeeDepartmentFilter"
                  :data="departmentTree"
                  :props="{ label: 'name', value: 'wechat_id' }"
                  placeholder="选择部门筛选"
                  clearable
                  style="width: 100%; margin-bottom: 10px"
                  @change="handleCreateEmployeeDepartmentChange"
                />
                <el-input
                  v-model="createEmployeeSearchKeyword"
                  placeholder="搜索员工姓名"
                  clearable
                  @input="handleCreateEmployeeSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              <div class="employee-list" @scroll="handleCreateEmployeeScroll">
                <div
                  v-for="employee in createEmployeeOptions"
                  :key="employee.id"
                  class="employee-item"
                  :class="{
                    selected: createForm.employee_ids.includes(employee.id),
                  }"
                  @click="toggleEmployeeSelection(employee)"
                >
                  <div class="employee-info">
                    <span class="employee-name">{{ employee.name }}</span>
                    <span class="employee-position">{{
                      employee.position
                    }}</span>
                    <span
                      class="employee-department"
                      v-if="
                        employee.departments && employee.departments.length > 0
                      "
                    >
                      {{ employee.departments.map((d) => d.name).join(", ") }}
                    </span>
                  </div>
                  <el-icon
                    v-if="createForm.employee_ids.includes(employee.id)"
                    class="selected-icon"
                  >
                    <Check />
                  </el-icon>
                </div>
                <div v-if="createEmployeePagination.hasMore" class="load-more">
                  <el-button
                    @click="fetchCreateEmployeeList(false)"
                    :loading="createEmployeeLoading"
                    size="small"
                  >
                    加载更多
                  </el-button>
                </div>
                <div
                  v-else-if="createEmployeeOptions.length > 0"
                  class="no-more"
                >
                  没有更多员工了
                </div>
              </div>
            </div>

            <!-- 右侧：已选员工 -->
            <div class="selected-employees-panel">
              <div class="panel-header">
                <span>已选员工 ({{ selectedEmployees.length }})</span>
                <el-button
                  size="small"
                  type="danger"
                  text
                  @click="clearAllSelectedEmployees"
                  v-if="selectedEmployees.length > 0"
                >
                  清空
                </el-button>
              </div>
              <div class="selected-employee-list">
                <div
                  v-for="employee in selectedEmployees"
                  :key="employee.id"
                  class="selected-employee-item"
                >
                  <div class="employee-info">
                    <span class="employee-name">{{ employee.name }}</span>
                    <span class="employee-position">{{
                      employee.position
                    }}</span>
                    <span
                      class="employee-department"
                      v-if="
                        employee.departments && employee.departments.length > 0
                      "
                    >
                      {{ employee.departments.map((d) => d.name).join(", ") }}
                    </span>
                  </div>
                  <el-button
                    size="small"
                    type="danger"
                    text
                    @click="removeSelectedEmployee(employee.id)"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                <div
                  v-if="selectedEmployees.length === 0"
                  class="empty-selected"
                >
                  暂未选择员工
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleCreate"
          :loading="createLoading"
          style="background-color: #1bb394; border-color: #1bb394"
        >
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑考核对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑考核"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="员工">
          <el-input :value="currentRecord?.employee?.name" disabled />
        </el-form-item>
        <el-form-item label="考核视频">
          <el-input :value="currentRecord?.assessment_video?.title" disabled />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editForm.status" style="width: 100%">
            <el-option label="未开始" :value="0" />
            <el-option label="进行中" :value="1" />
            <el-option label="已完成" :value="2" />
            <el-option label="已过期" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="考试时间"
          v-if="
            currentRecord?.assessment_video?.exam &&
            currentRecord?.assessment_video?.exam.type === 1
          "
        >
          <el-date-picker
            v-model="editForm.exam_start_time"
            type="datetime"
            placeholder="选择考试开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          <div class="form-tip">
            只有考试试卷需要设置考试时间，未到时间不允许参加考试
          </div>
        </el-form-item>
        <el-form-item label="视频进度">
          <el-slider
            v-model="editForm.video_progress"
            :min="0"
            :max="100"
            show-input
            :format-tooltip="(val: number) => `${val}%`"
          />
        </el-form-item>
        <el-form-item
          label="考试得分"
          v-if="currentRecord?.exam_score !== null"
        >
          <el-input-number
            v-model="editForm.exam_score"
            :min="0"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleUpdate"
          :loading="updateLoading"
          style="background-color: #1bb394; border-color: #1bb394"
        >
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 考试详情弹窗 -->
    <el-dialog
      v-model="showExamDetailDialog"
      title="考试详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="examDetailData" class="exam-detail">
        <div class="exam-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="label">考试得分</div>
                <div
                  class="value score"
                  :class="getScoreClass(examDetailData.score)"
                >
                  {{ examDetailData.score }}分
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="label">考试时长</div>
                <div class="value">
                  {{ formatDuration(examDetailData.duration) }}
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="label">正确题数</div>
                <div class="value">
                  {{ examDetailData.correct_count }}/{{
                    examDetailData.total_count
                  }}
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="label">考试结果</div>
                <div
                  class="value"
                  :class="examDetailData.is_passed ? 'passed' : 'failed'"
                >
                  {{ examDetailData.is_passed ? "及格" : "不及格" }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div
          class="exam-answers"
          v-if="examDetailData.answers && examDetailData.answers.length > 0"
        >
          <h4>答题详情</h4>
          <div class="answer-list">
            <div
              v-for="(answer, index) in examDetailData.answers"
              :key="answer.id"
              class="answer-item"
              :class="{
                correct: answer.is_correct,
                incorrect: !answer.is_correct,
              }"
            >
              <div class="question-header">
                <span class="question-number">第{{ index + 1 }}题</span>
                <span class="question-score">{{ answer.score }}分</span>
                <el-tag
                  :type="answer.is_correct ? 'success' : 'danger'"
                  size="small"
                >
                  {{ answer.is_correct ? "正确" : "错误" }}
                </el-tag>
              </div>
              <div class="question-content">
                <div class="question-title">{{ answer.question.title }}</div>
                <div class="question-text">{{ answer.question.content }}</div>

                <!-- 题目选项 -->
                <div
                  class="question-options"
                  v-if="
                    answer.question.options &&
                    answer.question.options.length > 0
                  "
                >
                  <div class="options-title">选项：</div>
                  <div
                    v-for="option in answer.question.options"
                    :key="option.label"
                    class="option-item"
                    :class="{
                      'user-selected': answer.answer === option.label,
                      'correct-option':
                        answer.question.correct_answer === option.label,
                      'wrong-selected':
                        answer.answer === option.label && !answer.is_correct,
                    }"
                  >
                    <span class="option-label">{{ option.label }}.</span>
                    <span class="option-content">{{ option.content }}</span>
                    <span
                      v-if="answer.answer === option.label"
                      class="selection-mark"
                      >（您的选择）</span
                    >
                    <span
                      v-if="answer.question.correct_answer === option.label"
                      class="correct-mark"
                      >（正确答案）</span
                    >
                  </div>
                </div>

                <div class="answer-info">
                  <div class="user-answer">
                    <strong>您的答案：</strong>{{ answer.answer }}
                  </div>
                  <div class="correct-answer">
                    <strong>正确答案：</strong
                    >{{ answer.question.correct_answer }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showExamDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 批量创建考核对话框 -->
    <el-dialog
      v-model="showBatchCreateDialog"
      title="批量创建考核"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-create-content">
        <div class="selected-employees">
          <h4>已选择员工 ({{ uniqueSelectedEmployees.length }}人)：</h4>
          <div class="employee-tags">
            <el-tag
              v-for="employee in uniqueSelectedEmployees"
              :key="employee.id"
              type="info"
              style="margin: 2px"
            >
              {{ employee.name }} - {{ employee.position }}
            </el-tag>
          </div>
        </div>

        <el-form
          :model="batchCreateForm"
          label-width="100px"
          style="margin-top: 20px"
        >
          <el-form-item label="考核视频" required>
            <el-select
              v-model="batchCreateForm.assessment_video_id"
              placeholder="请选择考核视频"
              clearable
              filterable
              remote
              :remote-method="handleBatchVideoSearch"
              :loading="batchVideoSearchLoading"
              style="width: 100%"
              @focus="handleBatchVideoSearchFocus"
              @change="handleBatchVideoChange"
            >
              <el-option
                v-for="video in batchVideoSearchOptions"
                :key="video.id"
                :label="video.title"
                :value="video.id"
              >
                <div class="video-option">
                  <span class="video-title">{{ video.title }}</span>
                  <span class="video-category" v-if="video.category">
                    {{ video.category.name }}
                  </span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="考试时间"
            v-if="
              selectedBatchAssessmentVideo &&
              selectedBatchAssessmentVideo.exam &&
              selectedBatchAssessmentVideo.exam.type === 1
            "
          >
            <el-date-picker
              v-model="batchCreateForm.exam_start_time"
              type="datetime"
              placeholder="选择考试开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
            <div class="form-tip">
              只有考试试卷需要设置考试时间，未到时间不允许参加考试
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showBatchCreateDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirmBatchCreate"
          style="background-color: #1bb394; border-color: #1bb394"
        >
          确认创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, Plus, Check, Close } from "@element-plus/icons-vue";
import {
  getAssessmentRecordList,
  createAssessmentRecord,
  updateAssessmentRecord,
  deleteAssessmentRecord,
  getExamDetail,
  type AssessmentRecord,
  type AssessmentRecordListRequest,
  type AssessmentRecordCreateRequest,
  type AssessmentRecordUpdateRequest,
  AssessmentRecordStatus,
} from "@/api/assessmentRecord";
import {
  getEmployeeList,
  searchEmployees,
  getDepartmentTree,
  type Employee,
  type Department,
} from "@/api/employee";
import {
  getAssessmentVideoList,
  searchAssessmentVideos,
} from "@/api/assessmentVideo";
import type { AssessmentVideo } from "@/types/assessmentVideo";
import { getDepartmentList } from "@/api/department";

// 响应式数据
const loading = ref(false);
const createLoading = ref(false);
const updateLoading = ref(false);
const departmentLoading = ref(false);
const tableData = ref<AssessmentRecord[]>([]);
const showCreateDialog = ref(false);
const showEditDialog = ref(false);
const showExamDetailDialog = ref(false);
const currentRecord = ref<AssessmentRecord | null>(null);
const examDetailData = ref<any>(null);
const employeeOptions = ref<Employee[]>([]);
const assessmentVideoOptions = ref<AssessmentVideo[]>([]);
const departmentOptions = ref<Department[]>([]);
const employeeSearchKeyword = ref("");
const employeePagination = ref({
  current: 1,
  size: 20,
  total: 0,
  hasMore: true,
});

let departmentTree = ref<Department[]>([]);

// 搜索相关
const employeeSearchOptions = ref<Employee[]>([]);
const videoSearchOptions = ref<AssessmentVideo[]>([]);
const employeeSearchLoading = ref(false);
const videoSearchLoading = ref(false);

// 创建考核相关
const createVideoSearchOptions = ref<AssessmentVideo[]>([]);
const createVideoSearchLoading = ref(false);
const createEmployeeOptions = ref<Employee[]>([]);
const createEmployeeLoading = ref(false);
const createEmployeeSearchKeyword = ref("");
const createEmployeeDepartmentFilter = ref<number | undefined>(undefined);
const createEmployeePagination = ref({
  current: 1,
  size: 20,
  total: 0,
  hasMore: true,
});

// 搜索表单
const searchForm = reactive<AssessmentRecordListRequest>({
  employee_id: undefined,
  assessment_video_id: undefined,
  department_id: undefined,
  status: undefined,
  is_passed: undefined,
});

// 批量选择相关
const selectedRecords = ref<AssessmentRecord[]>([]);
const showBatchCreateDialog = ref(false);
const batchCreateForm = reactive({
  assessment_video_id: undefined as number | undefined,
  exam_start_time: undefined as string | undefined,
});
const batchVideoSearchOptions = ref<AssessmentVideo[]>([]);
const batchVideoSearchLoading = ref(false);
const selectedBatchAssessmentVideo = ref<AssessmentVideo | null>(null);

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 创建表单
const createForm = reactive<AssessmentRecordCreateRequest>({
  employee_ids: [],
  assessment_video_id: undefined,
  exam_start_time: undefined,
});

// 编辑表单
const editForm = reactive<AssessmentRecordUpdateRequest>({
  status: undefined,
  video_progress: 0,
  exam_score: undefined,
  exam_start_time: undefined,
});

// 计算属性
// 已选员工列表
const selectedEmployees = computed(() => {
  return createEmployeeOptions.value.filter((emp) =>
    createForm.employee_ids.includes(emp.id)
  );
});

// 去重后的已选员工列表（用于批量创建）
const uniqueSelectedEmployees = computed(() => {
  const employeeMap = new Map();
  selectedRecords.value.forEach((record) => {
    if (record.employee && !employeeMap.has(record.employee_id)) {
      employeeMap.set(record.employee_id, record.employee);
    }
  });
  return Array.from(employeeMap.values());
});

// 选中的考核视频
const selectedAssessmentVideo = computed(() => {
  return createVideoSearchOptions.value.find(
    (video) => video.id === createForm.assessment_video_id
  );
});

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case AssessmentRecordStatus.NOT_STARTED:
      return "info";
    case AssessmentRecordStatus.IN_PROGRESS:
      return "warning";
    case AssessmentRecordStatus.COMPLETED:
      return "success";
    case AssessmentRecordStatus.EXPIRED:
      return "danger";
    default:
      return "info";
  }
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return "-";
  return new Date(dateTime).toLocaleString("zh-CN");
};

// 格式化考试时长
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}小时${minutes}分钟${secs}秒`;
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`;
  } else {
    return `${secs}秒`;
  }
};

// 获取分数样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return "excellent";
  if (score >= 80) return "good";
  if (score >= 60) return "pass";
  return "fail";
};

// 检查是否有考试试卷
const hasExamPaper = (record: AssessmentRecord) => {
  return record.assessment_video?.exam?.type === 1;
};

// 显示考试详情
const showExamDetail = async (record: AssessmentRecord) => {
  try {
    const response = await getExamDetail(record.id);
    if (response.code === 200) {
      examDetailData.value = response.data;
      showExamDetailDialog.value = true;
    } else {
      ElMessage.error(response.msg || "获取考试详情失败");
    }
  } catch (error) {
    console.error("获取考试详情失败:", error);
    ElMessage.error("获取考试详情失败");
  }
};

// 获取考核记录列表
const fetchAssessmentRecordList = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      page_size: pagination.size,
      ...searchForm,
    };
    const response = await getAssessmentRecordList(params);
    tableData.value = response.data || [];
    pagination.total = response.page.total || 0;
  } catch (error) {
    console.error("获取考核记录列表失败:", error);
    ElMessage.error("获取考核记录列表失败");
  } finally {
    loading.value = false;
  }
};

// 获取员工列表（支持分页）
const fetchEmployeeList = async (reset = false) => {
  try {
    if (reset) {
      employeePagination.value.current = 1;
      employeeOptions.value = [];
      employeePagination.value.hasMore = true;
    }

    if (!employeePagination.value.hasMore) return;

    const response = await getEmployeeList({
      page: employeePagination.value.current,
      page_size: employeePagination.value.size,
      name: employeeSearchKeyword.value,
    });

    const newEmployees = response.data || [];

    if (reset) {
      employeeOptions.value = newEmployees;
    } else {
      employeeOptions.value = [...employeeOptions.value, ...newEmployees];
    }

    employeePagination.value.total = response.page?.total || 0;
    employeePagination.value.hasMore =
      newEmployees.length === employeePagination.value.size;
    employeePagination.value.current++;
  } catch (error) {
    console.error("获取员工列表失败:", error);
    ElMessage.error("获取员工列表失败");
  }
};

// 获取考核视频列表
const fetchAssessmentVideoList = async () => {
  try {
    const response = await getAssessmentVideoList({ page: 1, page_size: 100 });
    assessmentVideoOptions.value = response.data || [];
  } catch (error) {
    console.error("获取考核视频列表失败:", error);
    ElMessage.error("获取考核视频列表失败");
  }
};

// 获取部门列表
const fetchDepartmentList = async (keyword = "") => {
  try {
    departmentLoading.value = true;
    const response = await getDepartmentList({
      page: 1,
      page_size: 100,
      name: keyword,
    });
    departmentOptions.value = response.data || [];
  } catch (error) {
    console.error("获取部门列表失败:", error);
    ElMessage.error("获取部门列表失败");
  } finally {
    departmentLoading.value = false;
  }
};

// 部门搜索处理
const handleDepartmentSearch = (keyword: string) => {
  fetchDepartmentList(keyword);
};

// 员工搜索处理
// 员工搜索
const handleEmployeeSearch = async (keyword: string) => {
  try {
    employeeSearchLoading.value = true;
    const response = await searchEmployees({
      keyword: keyword || undefined,
      department_id: searchForm.department_id,
      page: 1,
      page_size: 20,
    });
    employeeSearchOptions.value = response.data || [];
  } catch (error) {
    console.error("搜索员工失败:", error);
  } finally {
    employeeSearchLoading.value = false;
  }
};

// 员工搜索框获得焦点
const handleEmployeeSearchFocus = () => {
  handleEmployeeSearch("");
};

// 考核视频搜索
const handleVideoSearch = async (keyword: string) => {
  try {
    videoSearchLoading.value = true;
    const response = await searchAssessmentVideos({
      keyword: keyword || undefined,
      page: 1,
      page_size: 20,
    });
    videoSearchOptions.value = response.data || [];
  } catch (error) {
    console.error("搜索考核视频失败:", error);
  } finally {
    videoSearchLoading.value = false;
  }
};

// 考核视频搜索框获得焦点
const handleVideoSearchFocus = () => {
  handleVideoSearch("");
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchAssessmentRecordList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    employee_id: undefined,
    assessment_video_id: undefined,
    department_id: undefined,
    status: undefined,
  });
  employeeSearchOptions.value = [];
  videoSearchOptions.value = [];
  pagination.current = 1;
  fetchAssessmentRecordList();
};

// 显示创建考核对话框
const handleShowCreateDialog = () => {
  resetCreateForm();
  fetchCreateEmployeeList(true);
  showCreateDialog.value = true;
};

// 创建考核
const handleCreate = async () => {
  if (!createForm.assessment_video_id) {
    ElMessage.warning("请选择考核视频");
    return;
  }
  if (createForm.employee_ids.length === 0) {
    ElMessage.warning("请选择员工");
    return;
  }

  try {
    createLoading.value = true;
    await createAssessmentRecord(createForm);
    ElMessage.success("创建成功");
    showCreateDialog.value = false;
    resetCreateForm();
    fetchAssessmentRecordList();
  } catch (error) {
    console.error("创建考核失败:", error);
    ElMessage.error("创建考核失败");
  } finally {
    createLoading.value = false;
  }
};

// 创建考核视频搜索
const handleCreateVideoSearch = async (keyword: string) => {
  try {
    createVideoSearchLoading.value = true;
    const response = await searchAssessmentVideos({
      keyword: keyword || undefined,
      page: 1,
      page_size: 20,
    });
    createVideoSearchOptions.value = response.data || [];
  } catch (error) {
    console.error("搜索考核视频失败:", error);
  } finally {
    createVideoSearchLoading.value = false;
  }
};

// 创建考核视频搜索框获得焦点
const handleCreateVideoSearchFocus = () => {
  handleCreateVideoSearch("");
};

// 创建考核员工搜索
const handleCreateEmployeeSearch = () => {
  fetchCreateEmployeeList(true);
};

// 创建考核部门筛选改变
const handleCreateEmployeeDepartmentChange = () => {
  fetchCreateEmployeeList(true);
};

// 获取创建考核员工列表
const fetchCreateEmployeeList = async (reset = false) => {
  try {
    if (reset) {
      createEmployeePagination.value.current = 1;
      createEmployeeOptions.value = [];
      createEmployeePagination.value.hasMore = true;
    }

    if (!createEmployeePagination.value.hasMore) return;

    createEmployeeLoading.value = true;
    const response = await searchEmployees({
      keyword: createEmployeeSearchKeyword.value,
      department_id: createEmployeeDepartmentFilter.value,
      page: createEmployeePagination.value.current,
      page_size: createEmployeePagination.value.size,
    });

    const newEmployees = response.data || [];
    if (reset) {
      createEmployeeOptions.value = newEmployees;
    } else {
      createEmployeeOptions.value.push(...newEmployees);
    }

    createEmployeePagination.value.total = response.page.total;
    createEmployeePagination.value.hasMore =
      newEmployees.length === createEmployeePagination.value.size;
    createEmployeePagination.value.current++;
  } catch (error) {
    console.error("获取员工列表失败:", error);
  } finally {
    createEmployeeLoading.value = false;
  }
};

// 创建考核员工列表滚动处理
const handleCreateEmployeeScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
    if (createEmployeePagination.value.hasMore) {
      fetchCreateEmployeeList(false);
    }
  }
};

// 切换员工选择状态
const toggleEmployeeSelection = (employee: Employee) => {
  const index = createForm.employee_ids.indexOf(employee.id);
  if (index > -1) {
    createForm.employee_ids.splice(index, 1);
  } else {
    createForm.employee_ids.push(employee.id);
  }
};

// 移除已选员工
const removeSelectedEmployee = (employeeId: number) => {
  const index = createForm.employee_ids.indexOf(employeeId);
  if (index > -1) {
    createForm.employee_ids.splice(index, 1);
  }
};

// 清空所有已选员工
const clearAllSelectedEmployees = () => {
  createForm.employee_ids = [];
};

// 重置创建表单
const resetCreateForm = () => {
  createForm.employee_ids = [];
  createForm.assessment_video_id = undefined;
  createForm.exam_start_time = undefined;
  createEmployeeSearchKeyword.value = "";
  createEmployeeDepartmentFilter.value = undefined;
  createVideoSearchOptions.value = [];
  createEmployeeOptions.value = [];
  createEmployeePagination.value = {
    current: 1,
    size: 20,
    total: 0,
    hasMore: true,
  };
};

// 表格选择变化处理
const handleSelectionChange = (selection: AssessmentRecord[]) => {
  // 根据 employee_id 去重
  const uniqueRecords = selection.filter(
    (record, index, self) =>
      index === self.findIndex((r) => r.employee_id === record.employee_id)
  );
  selectedRecords.value = uniqueRecords;
};

// 批量创建考核
const handleBatchCreateAssessment = () => {
  if (uniqueSelectedEmployees.value.length === 0) {
    ElMessage.warning("请先选择要创建考核的员工");
    return;
  }
  showBatchCreateDialog.value = true;
};

// 批量视频搜索
const handleBatchVideoSearch = async (keyword: string) => {
  try {
    batchVideoSearchLoading.value = true;
    const response = await searchAssessmentVideos({
      keyword: keyword || "", // 空字符串时显示全部
      page: 1,
      page_size: 50,
    });
    batchVideoSearchOptions.value = response.data || [];
  } catch (error) {
    console.error("搜索考核视频失败:", error);
  } finally {
    batchVideoSearchLoading.value = false;
  }
};

// 批量视频搜索焦点处理
const handleBatchVideoSearchFocus = () => {
  if (batchVideoSearchOptions.value.length === 0) {
    handleBatchVideoSearch(""); // 空字符串显示全部
  }
};

// 处理批量视频选择变化
const handleBatchVideoChange = (videoId: number | undefined) => {
  if (videoId) {
    selectedBatchAssessmentVideo.value =
      batchVideoSearchOptions.value.find((video) => video.id === videoId) ||
      null;
  } else {
    selectedBatchAssessmentVideo.value = null;
  }
  // 清空考试时间
  batchCreateForm.exam_start_time = undefined;
};

// 确认批量创建考核
const handleConfirmBatchCreate = async () => {
  if (!batchCreateForm.assessment_video_id) {
    ElMessage.warning("请选择考核视频");
    return;
  }

  try {
    // 使用去重后的员工ID
    const employeeIds = uniqueSelectedEmployees.value.map(
      (employee) => employee.id
    );

    // 检查是否有员工已经存在相同考核视频的考核记录
    const existingRecords = tableData.value.filter(
      (record) =>
        employeeIds.includes(record.employee_id) &&
        record.assessment_video_id === batchCreateForm.assessment_video_id
    );

    if (existingRecords.length > 0) {
      const existingEmployeeNames = existingRecords
        .map((record) => record.employee?.name)
        .join("、");
      ElMessage.warning(
        `以下员工已存在该考核视频的考核记录：${existingEmployeeNames}`
      );
      return;
    }

    await createAssessmentRecord({
      employee_ids: employeeIds,
      assessment_video_id: batchCreateForm.assessment_video_id,
      exam_start_time: batchCreateForm.exam_start_time,
    });

    ElMessage.success(`成功为 ${employeeIds.length} 名员工创建考核`);
    showBatchCreateDialog.value = false;
    batchCreateForm.assessment_video_id = undefined;
    batchCreateForm.exam_start_time = undefined;
    selectedBatchAssessmentVideo.value = null;
    batchVideoSearchOptions.value = [];
    selectedRecords.value = [];
    fetchAssessmentRecordList();
  } catch (error) {
    console.error("批量创建考核失败:", error);
    ElMessage.error("批量创建考核失败");
  }
};

// 编辑考核
const handleEdit = (record: AssessmentRecord) => {
  currentRecord.value = record;
  editForm.status = record.status;
  editForm.video_progress = record.video_progress;
  editForm.exam_score = record.exam_score;
  editForm.exam_start_time = record.exam_start_time;
  showEditDialog.value = true;
};

// 更新考核
const handleUpdate = async () => {
  if (!currentRecord.value) return;

  try {
    updateLoading.value = true;
    await updateAssessmentRecord(currentRecord.value.id, editForm);
    ElMessage.success("更新成功");
    showEditDialog.value = false;
    fetchAssessmentRecordList();
  } catch (error) {
    console.error("更新考核失败:", error);
    ElMessage.error("更新考核失败");
  } finally {
    updateLoading.value = false;
  }
};

// 删除考核
const handleDelete = async (record: AssessmentRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除员工"${record.employee?.name}"的考核记录吗？`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await deleteAssessmentRecord(record.id);
    ElMessage.success("删除成功");
    fetchAssessmentRecordList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除考核失败:", error);
      ElMessage.error("删除考核失败");
    }
  }
};

// 获取部门树
const fetchDepartmentTree = async () => {
  try {
    const response = await getDepartmentTree();
    departmentTree.value = response.data || [];
  } catch (error) {
    console.error("获取部门树失败:", error);
  }
};

// 初始化
onMounted(() => {
  fetchAssessmentRecordList();
  fetchEmployeeList(true);
  fetchAssessmentVideoList();
  fetchDepartmentTree();
});
</script>

<style scoped>
.assessment-record-container {
  padding: 20px;
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  margin-top: 20px;
}

.employee-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.employee-list {
  max-height: 314px;
  overflow-y: auto;
}

.employee-item {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.employee-item:hover {
  background-color: #f5f7fa;
}

.employee-info {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}

.employee-name {
  font-weight: 500;
  color: #303133;
}

.employee-position {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.employee-department {
  font-size: 11px;
  color: #1bb394;
  margin-top: 2px;
}

.load-more {
  text-align: center;
  padding: 10px;
  border-top: 1px solid #ebeef5;
}

.no-more {
  text-align: center;
  padding: 10px;
  color: #909399;
  font-size: 12px;
  border-top: 1px solid #ebeef5;
}

.el-checkbox {
  width: 100%;
  margin-right: 0;
}

.el-checkbox__label {
  width: 100%;
}

/* 搜索选项样式 */
.employee-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.employee-name {
  font-weight: 500;
  color: #303133;
}

.employee-position {
  font-size: 12px;
  color: #909399;
}

.employee-department {
  font-size: 12px;
  color: #1bb394;
}

.video-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.video-title {
  font-weight: 500;
  color: #303133;
}

.video-category {
  font-size: 12px;
  color: #409eff;
}

/* 创建考核员工选择样式 */
.employee-selection-container {
  width: 100%;
  display: flex;
  gap: 20px;
  height: 400px;
}

.employee-search-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-filters {
  margin-bottom: 10px;
}

.employee-search-panel .employee-list {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  overflow-y: auto;
}

.employee-search-panel .employee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.employee-search-panel .employee-item:hover {
  background-color: #f5f7fa;
}

.employee-search-panel .employee-item.selected {
  background-color: #e6f7ff;
  border: 1px solid #1bb394;
}

.selected-icon {
  color: #1bb394;
  font-size: 16px;
}

.selected-employees-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
  font-weight: 500;
}

.selected-employee-list {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}

.selected-employee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  margin-bottom: 8px;
  background-color: #fff;
}

.selected-employee-item:last-child {
  margin-bottom: 0;
}

.empty-selected {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 考试详情弹窗样式 */
.exam-detail {
  padding: 20px 0;
}

.exam-summary {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.summary-item {
  text-align: center;
}

.summary-item .label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.summary-item .value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.summary-item .value.score.excellent {
  color: #f56c6c;
}

.summary-item .value.score.good {
  color: #e6a23c;
}

.summary-item .value.score.pass {
  color: #67c23a;
}

.summary-item .value.score.fail {
  color: #f56c6c;
}

.summary-item .value.passed {
  color: #67c23a;
}

.summary-item .value.failed {
  color: #f56c6c;
}

.exam-answers h4 {
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #1bb394;
  padding-bottom: 10px;
}

.answer-list {
  max-height: 400px;
  overflow-y: auto;
}

.answer-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fff;
}

.answer-item.correct {
  border: 1px solid #67c23a;
}

.answer-item.incorrect {
  border: 1px solid #f56c6c;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.question-number {
  font-weight: bold;
  color: #333;
}

.question-score {
  color: #666;
  font-size: 14px;
}

.question-content {
  margin-top: 10px;
}

.question-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.question-text {
  margin-bottom: 15px;
  color: #666;
  line-height: 1.6;
}

.answer-info {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.user-answer,
.correct-answer {
  margin-bottom: 5px;
}

.user-answer:last-child,
.correct-answer:last-child {
  margin-bottom: 0;
}

/* 题目选项样式 */
.question-options {
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.options-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
}

.option-item.user-selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.option-item.correct-option {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.option-item.wrong-selected {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.option-label {
  font-weight: bold;
  margin-right: 8px;
  min-width: 20px;
}

.option-content {
  flex: 1;
}

.selection-mark {
  color: #409eff;
  font-size: 12px;
  margin-left: 8px;
}

.correct-mark {
  color: #67c23a;
  font-size: 12px;
  margin-left: 8px;
}
</style>
