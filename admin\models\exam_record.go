package models

import (
	"time"

	"gorm.io/gorm"
)

// ExamRecordStatus 考试记录状态
type ExamRecordStatus int

const (
	ExamRecordStatusNotStarted ExamRecordStatus = 0 // 未开始
	ExamRecordStatusInProgress ExamRecordStatus = 1 // 进行中
	ExamRecordStatusCompleted  ExamRecordStatus = 2 // 已完成
	ExamRecordStatusTimeout    ExamRecordStatus = 3 // 超时
	ExamRecordStatusCancelled  ExamRecordStatus = 4 // 已取消
)

// ExamRecord 考试记录模型
type ExamRecord struct {
	ID                 uint             `json:"id" gorm:"primaryKey;autoIncrement"`
	EmployeeID         uint             `json:"employee_id" gorm:"type:int;not null;comment:员工ID"`
	ExamID             uint             `json:"exam_id" gorm:"type:int;not null;comment:试卷ID"`
	AssessmentRecordID *uint            `json:"assessment_record_id" gorm:"type:int;comment:考核记录ID（如果是考核中的考试）"`
	Status             ExamRecordStatus `json:"status" gorm:"type:tinyint(1);default:0;comment:状态 0未开始 1进行中 2已完成 3超时 4已取消"`
	StartTime          *time.Time       `json:"start_time" gorm:"type:datetime;comment:开始时间"`
	EndTime            *time.Time       `json:"end_time" gorm:"type:datetime;comment:结束时间"`
	SubmitTime         *time.Time       `json:"submit_time" gorm:"type:datetime;comment:提交时间"`
	Duration           int              `json:"duration" gorm:"type:int;default:0;comment:实际用时(秒)"`
	Score              int              `json:"score" gorm:"type:int;default:0;comment:得分"`
	TotalScore         int              `json:"total_score" gorm:"type:int;default:100;comment:总分"`
	PassScore          int              `json:"pass_score" gorm:"type:int;default:60;comment:及格分"`
	IsPassed           bool             `json:"is_passed" gorm:"type:tinyint(1);default:0;comment:是否及格"`
	CorrectCount       int              `json:"correct_count" gorm:"type:int;default:0;comment:正确题目数"`
	TotalCount         int              `json:"total_count" gorm:"type:int;default:0;comment:总题目数"`
	CreatedAt          time.Time        `json:"created_at"`
	UpdatedAt          time.Time        `json:"updated_at"`
	DeletedAt          gorm.DeletedAt   `json:"-" gorm:"index"`

	// 关联
	Employee         *Employee         `json:"employee,omitempty" gorm:"foreignKey:EmployeeID"`
	Exam             *Exam             `json:"exam,omitempty" gorm:"foreignKey:ExamID"`
	AssessmentRecord *AssessmentRecord `json:"assessment_record,omitempty" gorm:"foreignKey:AssessmentRecordID"`
	Answers          []ExamAnswer      `json:"answers,omitempty" gorm:"foreignKey:ExamRecordID"`
}

// GetStatusText 获取状态文本
func (er *ExamRecord) GetStatusText() string {
	switch er.Status {
	case ExamRecordStatusNotStarted:
		return "未开始"
	case ExamRecordStatusInProgress:
		return "进行中"
	case ExamRecordStatusCompleted:
		return "已完成"
	case ExamRecordStatusTimeout:
		return "超时"
	case ExamRecordStatusCancelled:
		return "已取消"
	default:
		return "未知"
	}
}

// ExamAnswer 考试答题记录模型
type ExamAnswer struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	ExamRecordID uint           `json:"exam_record_id" gorm:"type:int;not null;comment:考试记录ID"`
	QuestionID   uint           `json:"question_id" gorm:"type:int;not null;comment:题目ID"`
	Answer       string         `json:"answer" gorm:"type:varchar(10);comment:用户答案"`
	IsCorrect    bool           `json:"is_correct" gorm:"type:tinyint(1);default:0;comment:是否正确"`
	Score        int            `json:"score" gorm:"type:int;default:0;comment:得分"`
	AnswerTime   *time.Time     `json:"answer_time" gorm:"type:datetime;comment:答题时间"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	ExamRecord *ExamRecord `json:"exam_record,omitempty" gorm:"foreignKey:ExamRecordID"`
	Question   *Question   `json:"question,omitempty" gorm:"foreignKey:QuestionID"`
}

// ExamRecordResponse 考试记录响应数据
type ExamRecordResponse struct {
	ID                 uint                 `json:"id"`
	EmployeeID         uint                 `json:"employee_id"`
	ExamID             uint                 `json:"exam_id"`
	Title              string               `json:"title"`
	AssessmentRecordID *uint                `json:"assessment_record_id"`
	Status             ExamRecordStatus     `json:"status"`
	StatusText         string               `json:"status_text"`
	StartTime          *time.Time           `json:"start_time"`
	EndTime            *time.Time           `json:"end_time"`
	SubmitTime         *time.Time           `json:"submit_time"`
	Duration           int                  `json:"duration"`
	Score              int                  `json:"score"`
	TotalScore         int                  `json:"total_score"`
	PassScore          int                  `json:"pass_score"`
	IsPassed           bool                 `json:"is_passed"`
	CorrectCount       int                  `json:"correct_count"`
	TotalCount         int                  `json:"total_count"`
	Employee           *EmployeeResponse    `json:"employee,omitempty"`
	Exam               *ExamResponse        `json:"exam,omitempty"`
	Answers            []ExamAnswerResponse `json:"answers,omitempty"`
	CreatedAt          time.Time            `json:"created_at"`
	UpdatedAt          time.Time            `json:"updated_at"`
}

// ExamAnswerResponse 考试答题记录响应数据
type ExamAnswerResponse struct {
	ID           uint              `json:"id"`
	ExamRecordID uint              `json:"exam_record_id"`
	QuestionID   uint              `json:"question_id"`
	Answer       string            `json:"answer"`
	IsCorrect    bool              `json:"is_correct"`
	Score        int               `json:"score"`
	AnswerTime   *time.Time        `json:"answer_time"`
	Question     *QuestionResponse `json:"question,omitempty"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// ExamStartRequest 开始考试请求
type ExamStartRequest struct {
	ExamID             uint  `json:"exam_id" binding:"required"`
	AssessmentRecordID *uint `json:"assessment_record_id"`
}

// ExamSubmitRequest 提交考试请求
type ExamSubmitRequest struct {
	ExamRecordID uint                   `json:"exam_record_id" binding:"required"`
	Answers      []ExamSubmitAnswerItem `json:"answers" binding:"required"`
	SubmitType   string                 `json:"submit_type"` // manual: 手动提交, timeout: 超时提交, switch: 切换页面提交
}

// ExamSubmitAnswerItem 提交答案项
type ExamSubmitAnswerItem struct {
	QuestionID uint   `json:"question_id" binding:"required"`
	Answer     string `json:"answer"`
}

// ExamDetailResponse 考试详情响应（用于答题页面）
type ExamDetailResponse struct {
	ExamRecord ExamRecordResponse   `json:"exam_record"`
	Questions  []ExamQuestionDetail `json:"questions"`
	TimeLeft   int                  `json:"time_left"` // 剩余时间(秒)
}

// ExamQuestionDetail 考试题目详情（用于答题页面）
type ExamQuestionDetail struct {
	ID         uint             `json:"id"`
	QuestionID uint             `json:"question_id"`
	Score      int              `json:"score"`
	Sort       int              `json:"sort"`
	Question   QuestionResponse `json:"question"`
	UserAnswer string           `json:"user_answer,omitempty"` // 用户已选答案
	IsAnswered bool             `json:"is_answered"`           // 是否已答题
}
