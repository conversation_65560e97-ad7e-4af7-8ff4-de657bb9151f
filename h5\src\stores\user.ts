import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface UserInfo {
  id: number
  name: string
  department: string
  avatar: string
  phone?: string
  email?: string
}

export interface LearningStats {
  videoTime: number // 观看视频总时长（秒）
  practiceCount: number // 刷题总数量
  examCount: number // 考试总次数
  avgScore: number // 平均分数
}

export interface ExamRecord {
  id: number
  title: string
  examTime: string
  score: number
  duration: number // 考试用时（分钟）
  totalQuestions: number
  correctAnswers: number
  isPassed: boolean // 是否及格
}

export interface PracticeRecord {
  id: number
  title: string
  examTime: string
  score: number
  duration: number // 刷题用时（分钟）
  totalQuestions: number
  correctAnswers: number
  isPassed: boolean // 是否及格
}

export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo>({
    id: 1,
    name: '',
    department: '',
    avatar: '',
    phone: '13800138000',
    email: ''
  })

  const learningStats = ref<LearningStats>({
    videoTime: 7200, // 2小时
    practiceCount: 156,
    examCount: 8,
    avgScore: 85.2
  })

  const examRecords = ref<ExamRecord[]>([
  ])

  const practiceRecords = ref<PracticeRecord[]>([
  ])

  const loading = ref(false)

  // 获取用户信息
  const fetchUserInfo = async () => {
    loading.value = true
    try {
      // 使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 500))
      // 这里可以调用真实API
      // const response = await fetch('/api/user/info')
      // const data = await response.json()
      // userInfo.value = data.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取学习统计
  const fetchLearningStats = async () => {
    try {
      const { getLearningStats } = await import('@/api/user')
      const response = await getLearningStats()
      if (response.code === 200) {
        learningStats.value = {
          videoTime: response.data.video_time || 0,
          practiceCount: response.data.practice_count || 0,
          examCount: response.data.exam_count || 0,
          avgScore: response.data.avg_score || 0
        }
      }
    } catch (error) {
      console.error('获取学习统计失败:', error)
    }
  }

  // 获取考试记录
  const fetchExamRecords = async () => {
    try {
      const { getExamRecords } = await import('@/api/user')
      const response = await getExamRecords()
      if (response.code === 200) {
        examRecords.value = response.data.map((record: any) => ({
          id: record.id,
          title: record.title,
          examTime: record.exam_time,
          score: record.score,
          duration: record.duration,
          totalQuestions: record.total_questions,
          correctAnswers: record.correct_answers,
          isPassed: record.is_passed
        }))
      }
    } catch (error) {
      console.error('获取考试记录失败:', error)
    }
  }

  // 获取刷题记录
  const fetchPracticeRecords = async () => {
    try {
      const { getPracticeRecords } = await import('@/api/user')
      const response = await getPracticeRecords()
      if (response.code === 200) {
        practiceRecords.value = response.data.map((record: any) => ({
          id: record.id,
          title: record.title,
          examTime: record.exam_time,
          score: record.score,
          duration: record.duration,
          totalQuestions: record.total_questions,
          correctAnswers: record.correct_answers,
          isPassed: record.is_passed
        }))
      }
    } catch (error) {
      console.error('获取刷题记录失败:', error)
    }
  }

  //设置用户信息

  const setUserInfo = (payload:UserInfo):void=>{
    userInfo.value = payload
  }

  // 获取最近的考试记录（最多3条）
  const getRecentExams = () => {
    return examRecords.value
      .sort((a, b) => new Date(b.examTime).getTime() - new Date(a.examTime).getTime())
  }

  // 获取最近的刷题记录（最多3条）
  const getRecentPractices = () => {
    return practiceRecords.value
      .sort((a, b) => new Date(b.examTime).getTime() - new Date(a.examTime).getTime())
  }

  return {
    userInfo,
    learningStats,
    examRecords,
    practiceRecords,
    loading,
    fetchUserInfo,
    fetchLearningStats,
    fetchExamRecords,
    fetchPracticeRecords,
    getRecentExams,
    getRecentPractices,
    setUserInfo
  }
})
