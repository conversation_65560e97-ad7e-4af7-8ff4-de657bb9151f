<script setup lang="ts">
import TabBar from "./components/TabBar.vue";
</script>

<template>
  <div id="app">
    <router-view />
    <TabBar />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial,
    sans-serif;
  background-color: #f5f5f5;
}

#app {
  min-height: 100vh;
}
</style>
