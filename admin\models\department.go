package models

import (
	"time"

	"gorm.io/gorm"
)

// Department 部门模型
type Department struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	WechatID  int            `json:"wechat_id" gorm:"type:int;not null;uniqueIndex;comment:企业微信部门ID"`
	Name      string         `json:"name" gorm:"type:varchar(100);not null;comment:部门名称"`
	NameEn    string         `json:"name_en" gorm:"type:varchar(100);comment:部门英文名称"`
	ParentID  int            `json:"parent_id" gorm:"type:int;default:0;comment:父部门企业微信ID"`
	Sort      int            `json:"sort" gorm:"type:int;default:0;comment:在父部门中的次序值"`
	Status    int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1启用 0禁用"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联 - 移除外键约束，只保留逻辑关联
	Children []Department `json:"children,omitempty" gorm:"-"`
}

// DepartmentListRequest 部门列表请求参数
type DepartmentListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Name     string `form:"name"`
	Status   *int   `form:"status" binding:"omitempty,oneof=0 1"`
	ParentID *int   `form:"parent_id"`
}

// DepartmentResponse 部门响应数据
type DepartmentResponse struct {
	ID         uint                 `json:"id"`
	WechatID   int                  `json:"wechat_id"`
	Name       string               `json:"name"`
	NameEn     string               `json:"name_en"`
	ParentID   int                  `json:"parent_id"`
	Sort       int                  `json:"sort"`
	Status     int                  `json:"status"`
	StatusText string               `json:"status_text"`
	Children   []DepartmentResponse `json:"children,omitempty"`
	CreatedAt  time.Time            `json:"created_at"`
	UpdatedAt  time.Time            `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (d *Department) ToResponse() *DepartmentResponse {
	statusText := "禁用"
	if d.Status == 1 {
		statusText = "启用"
	}

	resp := &DepartmentResponse{
		ID:         d.ID,
		WechatID:   d.WechatID,
		Name:       d.Name,
		NameEn:     d.NameEn,
		ParentID:   d.ParentID,
		Sort:       d.Sort,
		Status:     d.Status,
		StatusText: statusText,
		CreatedAt:  d.CreatedAt,
		UpdatedAt:  d.UpdatedAt,
	}

	// 转换子部门
	if len(d.Children) > 0 {
		resp.Children = make([]DepartmentResponse, len(d.Children))
		for i, child := range d.Children {
			resp.Children[i] = *child.ToResponse()
		}
	}

	return resp
}
