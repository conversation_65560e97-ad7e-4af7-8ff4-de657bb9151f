package main

import (
	"ai_select_admin/config"
	"ai_select_admin/database"
	"ai_select_admin/internal/redis"
	"ai_select_admin/logger"
	"ai_select_admin/routes"
	"log"
	"os"

	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化配置
	config.Init()

	// 初始化日志
	logger.Init()

	// 初始化数据库
	database.Init()

	// 设置 Gin 模式
	if config.AppConfig.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	if err := redis.InitRedis(); err != nil {
		log.Fatalln(err.Error())
		os.Exit(-1)
	}

	// 创建路由
	r := routes.SetupRoutes()

	// 启动服务器
	port := config.AppConfig.Port
	if port == "" {
		port = "8080"
	}

	log.Printf("服务器启动在端口: %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatal("服务器启动失败:", err)
	}
}
