// 题库分类
export interface QuestionCategory {
  id: number
  name: string
  description: string
  sort: number
  status: number
  parent_id: number
  children?: QuestionCategory[]
  created_at: string
  updated_at: string
}

// 题库分类列表请求参数
export interface QuestionCategoryListRequest {
  page?: number
  page_size?: number
  name?: string
  status?: number
  parent_id?: number
}

// 创建题库分类请求参数
export interface QuestionCategoryCreateRequest {
  name: string
  description?: string
  sort?: number
  status?: number
  parent_id?: number
}

// 更新题库分类请求参数
export interface QuestionCategoryUpdateRequest {
  name: string
  description?: string
  sort?: number
  status?: number
  parent_id?: number
}
