package models

import (
	"time"

	"gorm.io/gorm"
)

// QuestionCategory 题库分类模型
type QuestionCategory struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Name        string         `json:"name" gorm:"type:varchar(100);not null;comment:分类名称"`
	Description string         `json:"description" gorm:"type:text;comment:分类描述"`
	Sort        int            `json:"sort" gorm:"type:int;default:0;comment:排序，数字越小越靠前"`
	Status      int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1启用 0禁用"`
	ParentID    uint           `json:"parent_id" gorm:"type:int;default:0;comment:父级分类ID，0为顶级分类"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// QuestionCategoryListRequest 题库分类列表请求参数
type QuestionCategoryListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Name     string `form:"name"`
	Status   *int   `form:"status" binding:"omitempty,oneof=0 1"`
	ParentID *uint  `form:"parent_id"`
}

// QuestionCategoryCreateRequest 创建题库分类请求参数
type QuestionCategoryCreateRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Sort        int    `json:"sort" binding:"omitempty,min=0"`
	Status      int    `json:"status" binding:"omitempty,oneof=0 1"`
	ParentID    uint   `json:"parent_id" binding:"omitempty,min=0"`
}

// QuestionCategoryUpdateRequest 更新题库分类请求参数
type QuestionCategoryUpdateRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Sort        int    `json:"sort" binding:"omitempty,min=0"`
	Status      int    `json:"status" binding:"omitempty,oneof=0 1"`
	ParentID    uint   `json:"parent_id" binding:"omitempty,min=0"`
}

// QuestionCategoryResponse 题库分类响应数据
type QuestionCategoryResponse struct {
	ID          uint                        `json:"id"`
	Name        string                      `json:"name"`
	Description string                      `json:"description"`
	Sort        int                         `json:"sort"`
	Status      int                         `json:"status"`
	ParentID    uint                        `json:"parent_id"`
	Children    []*QuestionCategoryResponse `json:"children,omitempty"`
	CreatedAt   time.Time                   `json:"created_at"`
	UpdatedAt   time.Time                   `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (qc *QuestionCategory) ToResponse() *QuestionCategoryResponse {
	return &QuestionCategoryResponse{
		ID:          qc.ID,
		Name:        qc.Name,
		Description: qc.Description,
		Sort:        qc.Sort,
		Status:      qc.Status,
		ParentID:    qc.ParentID,
		CreatedAt:   qc.CreatedAt,
		UpdatedAt:   qc.UpdatedAt,
	}
}
