import request from '@/utils/request'
import type { ApiResponse, PageInfo } from '@/types/common'

// 教师接口
export interface Teacher {
  id: number
  name: string
  avatar: string
  mobile: string
  email: string
  position: string
  status: number
  status_text: string
  sort: number
  created_at: string
  updated_at: string
}

// 教师列表请求参数
export interface TeacherListRequest {
  page?: number
  page_size?: number
  name?: string
  mobile?: string
  status?: number
}

// 教师创建请求
export interface TeacherCreateRequest {
  name: string
  avatar?: string
  mobile?: string
  email?: string
  position?: string
  status?: number
  sort?: number
}

// 教师更新请求
export interface TeacherUpdateRequest {
  name?: string
  avatar?: string
  mobile?: string
  email?: string
  position?: string
  status?: number
  sort?: number
}

interface PageResponse<T = any> extends ApiResponse<T> {
  page: PageInfo
}

// 获取教师列表
export function getTeacherList(params: TeacherListRequest): Promise<PageResponse<Teacher[]>> {
  return request({
    url: '/admin/teachers',
    method: 'get',
    params
  })
}

// 搜索教师（用于下拉选择）
export function searchTeachers(params: { keyword?: string; page?: number; page_size?: number }): Promise<PageResponse<Teacher[]>> {
  return request({
    url: '/admin/teachers/search',
    method: 'get',
    params
  })
}

// 获取教师详情
export function getTeacher(id: number): Promise<ApiResponse<Teacher>> {
  return request({
    url: `/admin/teachers/${id}`,
    method: 'get'
  })
}

// 创建教师
export function createTeacher(data: TeacherCreateRequest): Promise<ApiResponse<Teacher>> {
  return request({
    url: '/admin/teachers',
    method: 'post',
    data
  })
}

// 更新教师
export function updateTeacher(id: number, data: TeacherUpdateRequest): Promise<ApiResponse<Teacher>> {
  return request({
    url: `/admin/teachers/${id}`,
    method: 'put',
    data
  })
}

// 删除教师
export function deleteTeacher(id: number): Promise<ApiResponse<null>> {
  return request({
    url: `/admin/teachers/${id}`,
    method: 'delete'
  })
}
