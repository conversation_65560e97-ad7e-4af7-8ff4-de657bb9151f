import type { Category } from '@/types/category'

// 分类选项接口
export interface CategoryOption {
  id: number
  name: string
  label: string // 带层级显示的标签
  level: number // 层级深度
  disabled?: boolean
}

/**
 * 将分类树转换为带层级显示的平铺列表
 * @param categories 分类树数据
 * @param level 当前层级（从0开始）
 * @param prefix 前缀字符
 * @returns 平铺的分类选项列表
 */
export function flattenCategoryTree(
  categories: Category[], 
  level: number = 0, 
  prefix: string = ''
): CategoryOption[] {
  const result: CategoryOption[] = []
  
  categories.forEach(category => {
    // 生成层级显示标签
    const indent = '　'.repeat(level) // 使用全角空格缩进
    const label = level > 0 ? `${indent}├─ ${category.name}` : category.name
    
    result.push({
      id: category.id,
      name: category.name,
      label,
      level,
      disabled: category.status !== 1
    })
    
    // 递归处理子分类
    if (category.children && category.children.length > 0) {
      result.push(...flattenCategoryTree(category.children, level + 1, prefix))
    }
  })
  
  return result
}

/**
 * 将分类树转换为路径显示的平铺列表
 * @param categories 分类树数据
 * @param parentPath 父级路径
 * @returns 平铺的分类选项列表
 */
export function flattenCategoryTreeWithPath(
  categories: Category[], 
  parentPath: string = ''
): CategoryOption[] {
  const result: CategoryOption[] = []
  
  categories.forEach(category => {
    // 生成路径显示标签
    const currentPath = parentPath ? `${parentPath} / ${category.name}` : category.name
    
    result.push({
      id: category.id,
      name: category.name,
      label: currentPath,
      level: parentPath.split(' / ').length - 1,
      disabled: category.status !== 1
    })
    
    // 递归处理子分类
    if (category.children && category.children.length > 0) {
      result.push(...flattenCategoryTreeWithPath(category.children, currentPath))
    }
  })
  
  return result
}

/**
 * 根据分类ID获取完整路径
 * @param categories 分类树数据
 * @param targetId 目标分类ID
 * @param currentPath 当前路径
 * @returns 分类路径字符串
 */
export function getCategoryPath(
  categories: Category[], 
  targetId: number, 
  currentPath: string = ''
): string | null {
  for (const category of categories) {
    const newPath = currentPath ? `${currentPath} / ${category.name}` : category.name
    
    if (category.id === targetId) {
      return newPath
    }
    
    if (category.children && category.children.length > 0) {
      const result = getCategoryPath(category.children, targetId, newPath)
      if (result) {
        return result
      }
    }
  }
  
  return null
}
