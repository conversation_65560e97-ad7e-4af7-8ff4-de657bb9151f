{"name": "h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "moment": "^2.30.1", "pinia": "^3.0.3", "vant": "^4.9.21", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.13", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "postcss": "^8.5.6", "postcss-px-to-viewport": "^1.1.1", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}