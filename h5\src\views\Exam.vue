<template>
  <div class="exam-page">
    <!-- 头部 -->
    <van-nav-bar title="考试中心" fixed style="background-color: #1bb394" />

    <!-- 内容区域 -->
    <div class="content">
      <!-- 考试/刷题切换标签 -->
      <div class="exam-tabs">
        <van-tabs v-model:active="activeTab" @change="onTabChange">
          <van-tab title="考试" name="exam" />
          <van-tab title="刷题" name="practice" />
        </van-tabs>
      </div>

      <!-- 试卷列表 -->
      <div class="paper-list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <div
              v-for="paper in filteredPapers"
              :key="paper.id"
              class="paper-item"
            >
              <div class="paper-header">
                <div class="paper-title">
                  {{ paper.assessment_title }} - {{ paper.title }}
                </div>
                <van-button
                  type="primary"
                  size="small"
                  class="action-btn"
                  :disabled="activeTab === 'exam' && paper.exam_status === 1"
                  @click="handleActionClick(paper)"
                  :style="{
                    backgroundColor: '#1bb394',
                    borderColor: '#1bb394',
                  }"
                >
                  {{ getActionButtonText(paper) }}
                </van-button>
              </div>
              <div class="paper-info">
                <span class="paper-category">{{ paper.category_name }}</span>
                <span
                  class="paper-type"
                  :class="{ 'exam-type': activeTab === 'exam' }"
                >
                  {{ activeTab === "exam" ? "考试" : "刷题" }}
                </span>
                <span class="paper-count">{{ paper.question_count }}题</span>
                <!-- 显示考试状态和分数 -->
                <span
                  v-if="
                    paper.exam_status === 1 && paper.exam_score !== undefined
                  "
                  class="paper-score"
                >
                  得分: {{ paper.exam_score }}分
                </span>
              </div>
              <div class="paper-meta" v-if="paper.exam_start_time">
                <span class="paper-time"
                  >考试开始时间:{{
                    formatTime(paper.exam_start_time as string)
                  }}</span
                >
                <!-- 显示考试状态 -->
                <span
                  v-if="activeTab === 'exam'"
                  class="exam-status"
                  :class="getExamStatusClass(paper)"
                >
                  {{ getExamStatusText(paper) }}
                </span>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { showToast, showDialog } from "vant";
import { usePracticeStore } from "@/stores/practice";
import { startExam } from "@/api/user";

const practiceStore = usePracticeStore();
const router = useRouter();

const activeTab = ref("exam");
const refreshing = ref(false);
const loading = ref(false);
const finished = ref(false);

// 计算过滤后的试卷列表
const filteredPapers = computed(() => {
  let papers = practiceStore.papers;

  // 根据当前tab过滤试卷类型
  if (activeTab.value === "exam") {
    return papers.filter((paper) => paper.type === "exam");
  } else {
    return papers.filter((paper) => paper.type === "practice");
  }
});

// 格式化时间
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 获取按钮文本
const getActionButtonText = (paper: any) => {
  if (activeTab.value === "exam") {
    if (paper.exam_status === 1) {
      return "已完成";
    }
    return "开始考试";
  } else {
    // 刷题试卷始终显示"去练习"，可以重复做
    return "去练习";
  }
};

// 获取考试状态文本
const getExamStatusText = (paper: any) => {
  if (paper.exam_status === 1) {
    return "已完成";
  }
  return "未开始";
};

// 获取考试状态样式类
const getExamStatusClass = (paper: any) => {
  if (paper.exam_status === 1) {
    return "status-completed";
  }
  return "status-pending";
};

// 处理按钮点击
const handleActionClick = async (paper: any) => {
  if (activeTab.value === "exam") {
    // 考试试卷
    if (paper.exam_status === 1) {
      showToast("该考试已完成");
      return;
    }

    // 检查考试时间
    if (paper.exam_start_time) {
      const examStartTime = new Date(paper.exam_start_time);
      const now = new Date();
      if (now < examStartTime) {
        const timeStr = examStartTime.toLocaleString("zh-CN");
        showToast(`考试尚未开始\n开始时间： ${timeStr}`);
        return;
      }
    }

    // 确认开始考试
    showDialog({
      title: "开始考试",
      message: `确定要开始《${paper.title}》考试吗？\n考试时长：${
        paper.duration || 60
      }分钟`,
      showCancelButton: true,
      confirmButtonText: "开始考试",
      confirmButtonColor: "#1bb394",
    }).then(async () => {
      try {
        const response = await startExam({
          exam_id: paper.id,
          assessment_record_id: paper.assessment_record_id,
        });

        if (response.code === 200 && response.data) {
          //showToast("考试开始");
          // 跳转到答题页面
          router.push(`/exam/taking/${response.data.exam_record_id}`);
        } else {
          showToast(response.msg || "开始考试失败");
        }
      } catch (error) {
        console.error("开始考试失败:", error);
        showToast("开始考试失败，请重试");
      }
    });
  } else {
    // 刷题试卷
    try {
      const response = await startExam({
        exam_id: paper.id,
        assessment_record_id: paper.assessment_record_id,
      });

      if (response.code === 200 && response.data) {
        // 跳转到答题页面
        router.push(`/exam/taking/${response.data.exam_record_id}`);
      } else {
        showToast(response.msg || "开始刷题失败");
      }
    } catch (error) {
      console.error("开始刷题失败:", error);
      showToast("开始刷题失败，请重试");
    }
  }
};

// tab切换
const onTabChange = () => {
  // 可以在这里添加tab切换的逻辑
};

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true;
  await practiceStore.fetchPracticePapers();
  refreshing.value = false;
};

// 加载更多
const onLoad = () => {
  // 这里可以实现分页加载
  loading.value = false;
  finished.value = true;
};

onMounted(async () => {
  await practiceStore.fetchPracticePapers();
});
</script>

<style scoped>
.exam-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 50px;
}
:deep(.van-nav-bar__title) {
  color: #fff;
}
.content {
  padding-top: 46px;
}

.exam-tabs {
  background-color: #fff;
  position: sticky;
  top: 46px;
  z-index: 100;
  border-bottom: 1px solid #ebedf0;
}

:deep(.van-tabs__wrap) {
  border-bottom: 1px solid #ebedf0;
}

:deep(.van-tab--active) {
  color: #1bb394;
}

:deep(.van-tabs__line) {
  background-color: #1bb394;
}

.paper-list {
  padding: 12px;
}

.paper-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.paper-item:hover {
  box-shadow: 0 4px 12px rgba(27, 179, 148, 0.15);
}

.paper-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.paper-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
  margin-right: 12px;
}

.paper-assessment {
  margin-bottom: 8px;
}

.assessment-title {
  font-size: 14px;
  color: #1bb394;
  font-weight: 500;
}

.action-btn {
  background-color: #1bb394;
  border-color: #1bb394;
  border-radius: 16px;
  padding: 4px 16px;
  font-size: 12px;
}

.paper-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.paper-category {
  color: #1bb394;
  margin-right: 12px;
}

.paper-type {
  background-color: #f0f9ff;
  color: #1bb394;
  padding: 2px 8px;
  border-radius: 12px;
  margin-right: 12px;
}

.paper-type.exam-type {
  background-color: #fff2e8;
  color: #ff6b35;
}

.paper-count {
  color: #666;
}

.paper-score {
  color: #1bb394;
  font-weight: 500;
  margin-left: 12px;
}

.paper-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.exam-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
}

.status-completed {
  background-color: #f0f9ff;
  color: #1bb394;
}

.status-pending {
  background-color: #fff2e8;
  color: #ff6b35;
}

.action-btn:disabled {
  background-color: #ccc !important;
  border-color: #ccc !important;
  color: #999 !important;
}
</style>
