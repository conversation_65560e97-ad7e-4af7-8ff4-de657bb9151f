package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AssessmentVideoController struct{}

// ListAssessmentVideos 获取考核视频列表
func (avc *AssessmentVideoController) ListAssessmentVideos(c *gin.Context) {
	var req models.AssessmentVideoListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.AssessmentVideo{}).Preload("Category").Preload("Exam")

	// 按标题搜索
	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}

	// 按分类筛选
	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取考核视频总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var assessmentVideos []models.AssessmentVideo
	offset := (req.Page - 1) * req.PageSize
	if err := query.Preload("Videos", func(db *gorm.DB) *gorm.DB {
		// 对中间表记录进行排序
		return db.Order("sort ASC")
	}).Preload("Videos.Video").Order("id DESC").Offset(offset).Limit(req.PageSize).Find(&assessmentVideos).Error; err != nil {
		logger.Errorf("查询考核视频列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("查询考核视频列表成功: %v", assessmentVideos)

	// 转换为响应格式
	var responses []*models.AssessmentVideoResponse
	for _, av := range assessmentVideos {
		responses = append(responses, av.ToResponse())
	}

	// 计算分页信息
	pages := int(math.Ceil(float64(total) / float64(req.PageSize)))
	pageInfo := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Total:   total,
		Pages:   pages,
	}

	utils.PageSuccess(c, responses, pageInfo)
}

// SearchAssessmentVideos 搜索考核视频（用于下拉选择）
func (avc *AssessmentVideoController) SearchAssessmentVideos(c *gin.Context) {
	var req models.AssessmentVideoSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 构建查询条件
	query := database.DB.Model(&models.AssessmentVideo{}).Preload("Category").Preload("Exam")

	// 按标题搜索
	if req.Keyword != "" {
		query = query.Where("title LIKE ?", "%"+req.Keyword+"%")
	}

	// 按分类筛选
	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}

	// 只查询启用状态的考核视频
	query = query.Where("status = ?", 1)

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取考核视频总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var assessmentVideos []models.AssessmentVideo
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("title ASC").Offset(offset).Limit(req.PageSize).Find(&assessmentVideos).Error; err != nil {
		logger.Errorf("搜索考核视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var responses []*models.AssessmentVideoResponse
	for _, av := range assessmentVideos {
		responses = append(responses, av.ToResponse())
	}

	// 计算分页信息
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))
	page := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Pages:   totalPages,
		Total:   total,
	}
	utils.PageSuccess(c, responses, page)
}

// GetAssessmentVideo 获取考核视频详情
func (avc *AssessmentVideoController) GetAssessmentVideo(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考核视频ID")
		return
	}

	var assessmentVideo models.AssessmentVideo
	if err := database.DB.Preload("Category").Preload("Exam").Preload("Videos").Preload("Videos.Video").Preload("Videos.Video.Category").Preload("Teachers").Preload("Teachers.Teacher").Preload("PracticeExams").Preload("PracticeExams.Exam").First(&assessmentVideo, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "考核视频不存在")
		} else {
			logger.Errorf("查询考核视频失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, assessmentVideo.ToResponse())
}

// CreateAssessmentVideo 创建考核视频
func (avc *AssessmentVideoController) CreateAssessmentVideo(c *gin.Context) {
	var req models.AssessmentVideoCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 如果指定了分类，检查分类是否存在
	if req.CategoryID > 0 {
		var category models.AssessmentCategory
		if err := database.DB.First(&category, req.CategoryID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "考核分类不存在")
			} else {
				logger.Errorf("查询考核分类失败: %v", err)
				utils.InternalServerError(c, "系统错误")
			}
			return
		}
	}

	// 如果指定了试卷，检查试卷是否存在
	if req.ExamID > 0 {
		var exam models.Exam
		if err := database.DB.First(&exam, req.ExamID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "试卷不存在")
			} else {
				logger.Errorf("查询试卷失败: %v", err)
				utils.InternalServerError(c, "系统错误")
			}
			return
		}
	}

	// 验证视频是否存在（如果指定了视频）
	if len(req.VideoIDs) > 0 {
		var videoCount int64
		if err := database.DB.Model(&models.Video{}).Where("id IN ? AND status = 3", req.VideoIDs).Count(&videoCount).Error; err != nil {
			logger.Errorf("验证视频失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
		if int(videoCount) != len(req.VideoIDs) {
			utils.BadRequest(c, "存在无效的视频")
			return
		}
	}

	// 验证至少有视频或试卷中的一个
	if len(req.VideoIDs) == 0 && req.ExamID == 0 {
		utils.BadRequest(c, "至少需要关联视频或试卷中的一个")
		return
	}

	// 验证教师是否存在（如果指定了教师）
	if len(req.TeacherIDs) > 0 {
		var teacherCount int64
		if err := database.DB.Model(&models.Teacher{}).Where("id IN ? AND status = 1", req.TeacherIDs).Count(&teacherCount).Error; err != nil {
			logger.Errorf("验证教师失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
		if int(teacherCount) != len(req.TeacherIDs) {
			utils.BadRequest(c, "存在无效的教师")
			return
		}
	}

	// 验证刷题试卷是否存在（如果指定了刷题试卷）
	if len(req.PracticeExamIDs) > 0 {
		var examCount int64
		if err := database.DB.Model(&models.Exam{}).Where("id IN ? AND type = 2 AND status = 1", req.PracticeExamIDs).Count(&examCount).Error; err != nil {
			logger.Errorf("验证刷题试卷失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
		if int(examCount) != len(req.PracticeExamIDs) {
			utils.BadRequest(c, "存在无效的刷题试卷")
			return
		}
	}

	// 开始事务
	tx := database.DB.Begin()

	// 创建考核视频
	assessmentVideo := models.AssessmentVideo{
		Title:       req.Title,
		Description: req.Description,
		CategoryID:  req.CategoryID,
		ExamID:      req.ExamID,
		Status:      req.Status,
	}

	// 设置默认状态
	if assessmentVideo.Status == 0 {
		assessmentVideo.Status = 1
	}

	if err := tx.Create(&assessmentVideo).Error; err != nil {
		tx.Rollback()
		logger.Errorf("创建考核视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 创建视频关联
	for i, videoID := range req.VideoIDs {
		relation := models.AssessmentVideoRelation{
			AssessmentVideoID: assessmentVideo.ID,
			VideoID:           videoID,
			Sort:              i + 1,
		}

		if err := tx.Create(&relation).Error; err != nil {
			tx.Rollback()
			logger.Errorf("创建视频关联失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	// 创建教师关联
	for _, teacherID := range req.TeacherIDs {
		teacherRelation := models.AssessmentVideoTeacher{
			AssessmentVideoID: assessmentVideo.ID,
			TeacherID:         teacherID,
		}

		if err := tx.Create(&teacherRelation).Error; err != nil {
			tx.Rollback()
			logger.Errorf("创建教师关联失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	// 创建刷题试卷关联
	for _, examID := range req.PracticeExamIDs {
		practiceRelation := models.AssessmentVideoPractice{
			AssessmentVideoID: assessmentVideo.ID,
			ExamID:            examID,
		}

		if err := tx.Create(&practiceRelation).Error; err != nil {
			tx.Rollback()
			logger.Errorf("创建刷题试卷关联失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	tx.Commit()

	// 重新查询以获取关联数据
	if err := database.DB.Preload("Category").Preload("Exam").First(&assessmentVideo, assessmentVideo.ID).Error; err != nil {
		logger.Errorf("查询新创建的考核视频失败: %v", err)
	}

	logger.Infof("创建考核视频成功: %s", assessmentVideo.Title)
	utils.SuccessWithMsg(c, "创建成功", assessmentVideo.ToResponse())
}

// UpdateAssessmentVideo 更新考核视频
func (avc *AssessmentVideoController) UpdateAssessmentVideo(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考核视频ID")
		return
	}

	var req models.AssessmentVideoUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查找考核视频
	var assessmentVideo models.AssessmentVideo
	if err := database.DB.First(&assessmentVideo, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "考核视频不存在")
		} else {
			logger.Errorf("查询考核视频失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 如果指定了分类，检查分类是否存在
	if req.CategoryID > 0 {
		var category models.AssessmentCategory
		if err := database.DB.First(&category, req.CategoryID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "考核分类不存在")
			} else {
				logger.Errorf("查询考核分类失败: %v", err)
				utils.InternalServerError(c, "系统错误")
			}
			return
		}
	}

	// 如果指定了试卷，检查试卷是否存在
	if req.ExamID > 0 {
		var exam models.Exam
		if err := database.DB.First(&exam, req.ExamID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "试卷不存在")
			} else {
				logger.Errorf("查询试卷失败: %v", err)
				utils.InternalServerError(c, "系统错误")
			}
			return
		}
	}

	// 验证视频是否存在
	var videoCount int64
	if err := database.DB.Model(&models.Video{}).Where("id IN ? AND status = 3", req.VideoIDs).Count(&videoCount).Error; err != nil {
		logger.Errorf("验证视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if int(videoCount) != len(req.VideoIDs) {
		utils.BadRequest(c, "存在无效的视频")
		return
	}

	// 验证教师是否存在（如果指定了教师）
	if len(req.TeacherIDs) > 0 {
		var teacherCount int64
		if err := database.DB.Model(&models.Teacher{}).Where("id IN ? AND status = 1", req.TeacherIDs).Count(&teacherCount).Error; err != nil {
			logger.Errorf("验证教师失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
		if int(teacherCount) != len(req.TeacherIDs) {
			utils.BadRequest(c, "存在无效的教师")
			return
		}
	}

	// 验证刷题试卷是否存在（如果指定了刷题试卷）
	if len(req.PracticeExamIDs) > 0 {
		var examCount int64
		if err := database.DB.Model(&models.Exam{}).Where("id IN ? AND type = 2 AND status = 1", req.PracticeExamIDs).Count(&examCount).Error; err != nil {
			logger.Errorf("验证刷题试卷失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
		if int(examCount) != len(req.PracticeExamIDs) {
			utils.BadRequest(c, "存在无效的刷题试卷")
			return
		}
	}

	// 开始事务
	tx := database.DB.Begin()

	// 更新考核视频
	assessmentVideo.Title = req.Title
	assessmentVideo.Description = req.Description
	assessmentVideo.CategoryID = req.CategoryID
	assessmentVideo.ExamID = req.ExamID
	assessmentVideo.Status = req.Status

	if err := tx.Save(&assessmentVideo).Error; err != nil {
		tx.Rollback()
		logger.Errorf("更新考核视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 删除原有的视频关联
	if err := tx.Where("assessment_video_id = ?", uint(id)).Delete(&models.AssessmentVideoRelation{}).Error; err != nil {
		tx.Rollback()
		logger.Errorf("删除原有视频关联失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 删除原有的教师关联
	if err := tx.Where("assessment_video_id = ?", uint(id)).Delete(&models.AssessmentVideoTeacher{}).Error; err != nil {
		tx.Rollback()
		logger.Errorf("删除原有教师关联失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 删除原有的刷题试卷关联
	if err := tx.Where("assessment_video_id = ?", uint(id)).Delete(&models.AssessmentVideoPractice{}).Error; err != nil {
		tx.Rollback()
		logger.Errorf("删除原有刷题试卷关联失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 创建新的视频关联
	for i, videoID := range req.VideoIDs {
		relation := models.AssessmentVideoRelation{
			AssessmentVideoID: assessmentVideo.ID,
			VideoID:           videoID,
			Sort:              i + 1,
		}

		if err := tx.Create(&relation).Error; err != nil {
			tx.Rollback()
			logger.Errorf("创建视频关联失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	// 创建新的教师关联
	for _, teacherID := range req.TeacherIDs {
		teacherRelation := models.AssessmentVideoTeacher{
			AssessmentVideoID: assessmentVideo.ID,
			TeacherID:         teacherID,
		}

		if err := tx.Create(&teacherRelation).Error; err != nil {
			tx.Rollback()
			logger.Errorf("创建教师关联失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	// 创建新的刷题试卷关联
	for _, examID := range req.PracticeExamIDs {
		practiceRelation := models.AssessmentVideoPractice{
			AssessmentVideoID: assessmentVideo.ID,
			ExamID:            examID,
		}

		if err := tx.Create(&practiceRelation).Error; err != nil {
			tx.Rollback()
			logger.Errorf("创建刷题试卷关联失败: %v", err)
			utils.InternalServerError(c, "系统错误")
			return
		}
	}

	tx.Commit()

	// 重新查询以获取关联数据
	if err := database.DB.Preload("Category").Preload("Exam").Preload("Videos").Preload("Videos.Video").Preload("Videos.Video.Category").Preload("Teachers").Preload("Teachers.Teacher").Preload("PracticeExams").Preload("PracticeExams.Exam").First(&assessmentVideo, assessmentVideo.ID).Error; err != nil {
		logger.Errorf("查询更新后的考核视频失败: %v", err)
	}

	logger.Infof("更新考核视频成功: %s", assessmentVideo.Title)
	utils.SuccessWithMsg(c, "更新成功", assessmentVideo.ToResponse())
}

// DeleteAssessmentVideo 删除考核视频
func (avc *AssessmentVideoController) DeleteAssessmentVideo(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的考核视频ID")
		return
	}

	// 查找考核视频
	var assessmentVideo models.AssessmentVideo
	if err := database.DB.First(&assessmentVideo, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "考核视频不存在")
		} else {
			logger.Errorf("查询考核视频失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 开始事务
	tx := database.DB.Begin()

	// 删除视频关联
	if err := tx.Where("assessment_video_id = ?", uint(id)).Delete(&models.AssessmentVideoRelation{}).Error; err != nil {
		tx.Rollback()
		logger.Errorf("删除视频关联失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 软删除考核视频
	if err := tx.Delete(&assessmentVideo).Error; err != nil {
		tx.Rollback()
		logger.Errorf("删除考核视频失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	tx.Commit()

	logger.Infof("删除考核视频成功: %s", assessmentVideo.Title)
	utils.SuccessWithMsg(c, "删除成功", nil)
}
