import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getExamList, type ExamPaper } from '@/api/user'

// 考试答题状态管理
export const useExamTakingStore = defineStore('examTaking', () => {
  // 用户答案 - 以考试记录ID为key，题目ID为子key
  const userAnswers = ref<Record<number, Record<number, string>>>({})

  // 设置答案
  const setAnswer = (examRecordId: number, questionId: number, answer: string) => {
    if (!userAnswers.value[examRecordId]) {
      userAnswers.value[examRecordId] = {}
    }
    userAnswers.value[examRecordId][questionId] = answer
  }

  // 获取答案
  const getAnswer = (examRecordId: number, questionId: number): string | undefined => {
    return userAnswers.value[examRecordId]?.[questionId]
  }

  // 获取考试的所有答案
  const getExamAnswers = (examRecordId: number): Record<number, string> => {
    return userAnswers.value[examRecordId] || {}
  }

  // 清除考试答案（考试结束后调用）
  const clearExamAnswers = (examRecordId: number) => {
    if (userAnswers.value[examRecordId]) {
      delete userAnswers.value[examRecordId]
    }
  }

  // 初始化考试答案（从服务器获取已保存的答案）
  const initExamAnswers = (examRecordId: number, answers: Record<number, string>) => {
    userAnswers.value[examRecordId] = { ...answers }
  }

  return {
    userAnswers,
    setAnswer,
    getAnswer,
    getExamAnswers,
    clearExamAnswers,
    initExamAnswers
  }
})

export interface Paper {
  id: number
  title: string
  description: string
  type: string // 'exam' | 'practice'
  category_id: number
  category_name: string
  assessment_title: string // 考核标题
  question_count: number
  created_at: string
  updated_at: string
  assessment_record_id?: number
  exam_status?: number
  exam_score?: number
  exam_start_time?: string
  duration?: number
}

export interface PaperCategory {
  id: number
  name: string
  parent_id: number
  sort: number
  created_at: string
  updated_at: string
}

export const usePracticeStore = defineStore('practice', () => {
  const papers = ref<Paper[]>([])
  const categories = ref<PaperCategory[]>([])
  const loading = ref(false)

  // 获取试卷列表
  const fetchPracticePapers = async () => {
    loading.value = true
    try {
      const response = await getExamList()
      console.log('API响应:', response)
      if (response.code === 200 && response.data) {
        // 确保数组不为null，如果为null则使用空数组
        const examPapersData = response.data.exam_papers || []
        const practicePapersData = response.data.practice_papers || []
        console.log('考试试卷数据:', examPapersData)
        console.log('刷题试卷数据:', practicePapersData)

        // 合并考试试卷和刷题试卷，并转换格式
        const examPapers = examPapersData.map((paper: ExamPaper) => ({
          id: paper.id,
          title: paper.title,
          description: paper.description,
          type: 'exam',
          category_id: 0,
          category_name: paper.category_name,
          assessment_title: paper.assessment_title,
          question_count: paper.question_count,
          created_at: paper.created_at,
          updated_at: paper.updated_at,
          assessment_record_id: paper.assessment_record_id,
          exam_status: paper.exam_status,
          exam_score: paper.exam_score,
          exam_start_time: paper.exam_start_time,
          duration: paper.duration
        }))

        const practicePapers = practicePapersData.map((paper: ExamPaper) => ({
          id: paper.id,
          title: paper.title,
          description: paper.description,
          type: 'practice',
          category_id: 0,
          category_name: paper.category_name,
          assessment_title: paper.assessment_title,
          question_count: paper.question_count,
          created_at: paper.created_at,
          updated_at: paper.updated_at,
          assessment_record_id: paper.assessment_record_id,
          exam_status: paper.exam_status,
          exam_score: paper.exam_score,
          duration: paper.duration,
        }))

        papers.value = [...examPapers, ...practicePapers]
      } else {
        // 如果API调用失败或返回错误，设置为空数组
        papers.value = []
      }
    } catch (error) {
      console.error('获取试卷列表失败:', error)
      console.error('错误详情:', error)
      // 出错时也设置为空数组，避免undefined导致的错误
      papers.value = []
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      // 暂时不需要分类功能
      categories.value = []
    } catch (error) {
      console.error('获取分类列表失败:', error)
    }
  }

  return {
    papers,
    categories,
    loading,
    fetchPracticePapers,
    fetchCategories
  }
})
