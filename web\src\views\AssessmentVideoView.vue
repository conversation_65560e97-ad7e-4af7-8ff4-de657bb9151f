<template>
  <div class="assessment-video-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="考核标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入考核标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="考核分类">
          <el-select
            v-model="searchForm.category_id"
            placeholder="请选择分类"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="option in assessmentCategorySelectOptions"
              :key="option.id"
              :label="option.label"
              :value="option.id"
              :disabled="option.disabled"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleSearch"
            style="
              background-color: rgb(27, 179, 148);
              border-color: rgb(27, 179, 148);
            "
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button
            type="success"
            @click="handleAdd"
            style="
              background-color: rgb(27, 179, 148);
              border-color: rgb(27, 179, 148);
            "
          >
            <el-icon><Plus /></el-icon>
            新增考核
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table v-loading="loading" :data="tableData" style="width: 100%">
        <el-table-column
          prop="title"
          label="考核标题"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="description"
          label="描述"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="category.name"
          label="分类"
          width="120"
          align="center"
        />
        <el-table-column
          prop="exam.title"
          label="关联考试试卷"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="视频数量" width="100" align="center">
          <template #default="{ row }">
            {{ row.videos?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="created_at"
          label="创建时间"
          width="180"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑抽屉 -->
    <el-drawer
      v-model="dialogVisible"
      :title="editingId ? '编辑考核' : '新增考核'"
      direction="rtl"
      size="70%"
      :destroy-on-close="true"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="考核标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入考核标题"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核分类" prop="category_id">
              <el-select
                v-model="formData.category_id"
                placeholder="请选择分类"
                style="width: 100%"
              >
                <el-option
                  v-for="option in assessmentCategorySelectOptions"
                  :key="option.id"
                  :label="option.label"
                  :value="option.id"
                  :disabled="option.disabled"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="考核描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入考核描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="关联刷题试卷" prop="practice_exam_ids">
          <div class="practice-exam-selection">
            <!-- 刷题试卷搜索 -->
            <div class="practice-exam-search">
              <el-form :model="practiceExamSearchForm" inline>
                <el-form-item label="试卷名称">
                  <el-input
                    v-model="practiceExamSearchForm.title"
                    placeholder="请输入刷题试卷名称"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handlePracticeExamSearch"
                    >搜索刷题试卷</el-button
                  >
                </el-form-item>
              </el-form>
            </div>

            <!-- 刷题试卷列表 -->
            <div class="practice-exam-list">
              <el-row :gutter="20">
                <el-col :span="12">
                  <h4>可选刷题试卷</h4>
                  <el-table
                    ref="practiceExamTableRef"
                    v-loading="practiceExamLoading"
                    :data="availablePracticeExams"
                    style="width: 100%"
                    max-height="300px"
                    @selection-change="handlePracticeExamSelectionChange"
                  >
                    <el-table-column type="selection" width="55" />
                    <el-table-column
                      prop="title"
                      label="试卷名称"
                      min-width="150"
                      show-overflow-tooltip
                    />
                    <el-table-column
                      prop="description"
                      label="描述"
                      min-width="120"
                      show-overflow-tooltip
                    />
                    <el-table-column
                      prop="total_score"
                      label="总分"
                      width="80"
                      align="center"
                    />
                  </el-table>
                </el-col>
                <el-col :span="12">
                  <h4>已选刷题试卷 ({{ selectedPracticeExams.length }})</h4>
                  <el-table
                    :data="selectedPracticeExams"
                    style="width: 100%"
                    max-height="300px"
                  >
                    <el-table-column
                      prop="title"
                      label="试卷名称"
                      min-width="150"
                      show-overflow-tooltip
                    />
                    <el-table-column
                      prop="description"
                      label="描述"
                      min-width="120"
                      show-overflow-tooltip
                    />
                    <el-table-column label="操作" width="80">
                      <template #default="{ $index }">
                        <el-button
                          type="danger"
                          size="small"
                          @click="removePracticeExam($index)"
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="关联考试试卷" prop="exam_id">
          <div class="exam-selection">
            <!-- 试卷搜索 -->
            <div class="exam-search">
              <el-form :model="examSearchForm" inline>
                <el-form-item label="试卷名称">
                  <el-input
                    v-model="examSearchForm.title"
                    placeholder="请输入试卷名称"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleExamSearch"
                    >搜索试卷</el-button
                  >
                  <el-button @click="handleExamReset">重置</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 试卷列表 -->
            <div class="exam-list">
              <el-table
                ref="examTableRef"
                v-loading="examLoading"
                :data="availableExams"
                style="width: 100%"
                max-height="300px"
                highlight-current-row
                @current-change="handleExamSelectionChange"
              >
                <el-table-column
                  prop="title"
                  label="试卷名称"
                  min-width="200"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="description"
                  label="描述"
                  min-width="150"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="total_score"
                  label="总分"
                  width="80"
                  align="center"
                />
                <el-table-column
                  prop="pass_score"
                  label="及格分"
                  width="80"
                  align="center"
                />
                <el-table-column
                  prop="duration"
                  label="时长(分钟)"
                  width="100"
                  align="center"
                />
                <el-table-column
                  prop="status"
                  label="状态"
                  width="80"
                  align="center"
                >
                  <template #default="{ row }">
                    <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                      {{ row.status === 1 ? "启用" : "禁用" }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="created_at"
                  label="创建时间"
                  width="150"
                  align="center"
                >
                  <template #default="{ row }">
                    {{ formatDateTime(row.created_at) }}
                  </template>
                </el-table-column>
              </el-table>

              <!-- 当前选中的试卷 -->
              <div v-if="selectedExam" class="selected-exam">
                <h4>已选试卷</h4>
                <el-card class="exam-card">
                  <div class="exam-info">
                    <div class="exam-title">{{ selectedExam.title }}</div>
                    <div class="exam-details">
                      <span>总分: {{ selectedExam.total_score }}</span>
                      <span>及格分: {{ selectedExam.pass_score }}</span>
                      <span>时长: {{ selectedExam.duration }}分钟</span>
                    </div>
                    <div class="exam-description">
                      {{ selectedExam.description }}
                    </div>
                  </div>
                  <el-button
                    type="danger"
                    size="small"
                    @click="clearExamSelection"
                  >
                    取消选择
                  </el-button>
                </el-card>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="选择视频" prop="video_ids">
          <div class="video-selection">
            <!-- 视频搜索 -->
            <div class="video-search">
              <el-form :model="videoSearchForm" inline>
                <el-form-item label="视频标题">
                  <el-input
                    v-model="videoSearchForm.title"
                    placeholder="请输入视频标题"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item label="视频分类">
                  <el-select
                    v-model="videoSearchForm.category_id"
                    placeholder="请选择分类"
                    clearable
                    style="width: 150px"
                  >
                    <el-option
                      v-for="option in videoCategorySelectOptions"
                      :key="option.id"
                      :label="option.label"
                      :value="option.id"
                      :disabled="option.disabled"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleVideoSearch"
                    >搜索视频</el-button
                  >
                </el-form-item>
              </el-form>
            </div>

            <!-- 视频列表 -->
            <div class="video-list">
              <el-row :gutter="20">
                <el-col :span="12">
                  <h4>可选视频</h4>
                  <el-table
                    ref="videoTableRef"
                    v-loading="videoLoading"
                    :data="availableVideos"
                    style="width: 100%"
                    max-height="300px"
                    @selection-change="handleVideoSelectionChange"
                  >
                    <el-table-column type="selection" width="55" />
                    <el-table-column
                      prop="title"
                      label="视频标题"
                      min-width="150"
                      show-overflow-tooltip
                    />
                    <el-table-column
                      prop="category_name"
                      label="分类"
                      width="100"
                    />
                    <el-table-column label="时长" width="80" align="center">
                      <template #default="{ row }">
                        {{ formatDuration(row.duration) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
                <el-col :span="12">
                  <h4>已选视频 ({{ selectedVideos.length }})</h4>
                  <el-table
                    :data="selectedVideos"
                    style="width: 100%"
                    max-height="300px"
                  >
                    <el-table-column
                      prop="title"
                      label="视频标题"
                      min-width="150"
                      show-overflow-tooltip
                    />
                    <el-table-column
                      prop="category_name"
                      label="分类"
                      width="100"
                    />
                    <el-table-column label="操作" width="80">
                      <template #default="{ $index }">
                        <el-button
                          type="danger"
                          size="small"
                          @click="removeVideo($index)"
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="关联教师" prop="teacher_ids">
          <div class="teacher-selection">
            <!-- 教师搜索 -->
            <div class="teacher-search">
              <el-form :model="teacherSearchForm" inline>
                <el-form-item label="教师姓名">
                  <el-input
                    v-model="teacherSearchForm.keyword"
                    placeholder="请输入教师姓名或手机号"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleTeacherSearch"
                    >搜索教师</el-button
                  >
                </el-form-item>
              </el-form>
            </div>

            <!-- 教师列表 -->
            <div class="teacher-list">
              <el-row :gutter="20">
                <el-col :span="12">
                  <h4>可选教师</h4>
                  <el-table
                    ref="teacherTableRef"
                    v-loading="teacherLoading"
                    :data="availableTeachers"
                    style="width: 100%"
                    max-height="300px"
                    @selection-change="handleTeacherSelectionChange"
                  >
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="头像" width="80" align="center">
                      <template #default="{ row }">
                        <el-avatar :size="40" :src="row.avatar" :alt="row.name">
                          <el-icon><User /></el-icon>
                        </el-avatar>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="name"
                      label="教师姓名"
                      min-width="100"
                      show-overflow-tooltip
                    />
                    <el-table-column
                      prop="mobile"
                      label="手机号码"
                      width="120"
                    />
                    <el-table-column prop="position" label="职位" width="100" />
                  </el-table>
                </el-col>
                <el-col :span="12">
                  <h4>已选教师 ({{ selectedTeachers.length }})</h4>
                  <el-table
                    :data="selectedTeachers"
                    style="width: 100%"
                    max-height="300px"
                  >
                    <el-table-column label="头像" width="80" align="center">
                      <template #default="{ row }">
                        <el-avatar :size="40" :src="row.avatar" :alt="row.name">
                          <el-icon><User /></el-icon>
                        </el-avatar>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="name"
                      label="教师姓名"
                      min-width="100"
                      show-overflow-tooltip
                    />
                    <el-table-column
                      prop="mobile"
                      label="手机号码"
                      width="120"
                    />
                    <el-table-column label="操作" width="80">
                      <template #default="{ $index }">
                        <el-button
                          type="danger"
                          size="small"
                          @click="removeTeacher($index)"
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form-item>

        <!-- 同时创建补考选项 -->
        <el-form-item v-if="!editingId" label="补考设置">
          <el-checkbox v-model="formData.create_makeup_exam">
            同时创建补考
          </el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules,
} from "element-plus";
import { Search, Refresh, Plus, User } from "@element-plus/icons-vue";
import {
  getAssessmentVideoList,
  createAssessmentVideo,
  updateAssessmentVideo,
  deleteAssessmentVideo,
  getAssessmentVideo,
} from "@/api/assessmentVideo";
import { getAssessmentCategoryTree } from "@/api/assessmentCategory";
import { getExamList } from "@/api/exam";
import { getVideoList } from "@/api/video";
import { getCategoryTree } from "@/api/category";
import { searchTeachers } from "@/api/teacher";
import type {
  AssessmentVideo,
  VideoSelectOption,
} from "@/types/assessmentVideo";
import type { Exam } from "@/types/exam";
import type { Video, VideoListRequest } from "@/types/video";
import type { Teacher } from "@/types/teacher";
import {
  flattenCategoryTreeWithPath,
  type CategoryOption,
} from "@/utils/category";

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const videoLoading = ref(false);
const examLoading = ref(false);
const teacherLoading = ref(false);
const practiceExamLoading = ref(false);
const dialogVisible = ref(false);
const editingId = ref<number | null>(null);
const formRef = ref<FormInstance>();
const videoTableRef = ref();
const examTableRef = ref();
const teacherTableRef = ref();
const practiceExamTableRef = ref();

// 搜索表单
const searchForm = reactive({
  title: "",
  category_id: undefined as number | undefined,
  status: undefined as number | undefined,
});

// 视频搜索表单
const videoSearchForm = reactive({
  title: "",
  category_id: undefined as number | undefined,
});

// 试卷搜索表单
const examSearchForm = reactive({
  title: "",
});

// 教师搜索表单
const teacherSearchForm = reactive({
  keyword: "",
});

// 刷题试卷搜索表单
const practiceExamSearchForm = reactive({
  title: "",
});

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 表格数据
const tableData = ref<AssessmentVideo[]>([]);
const assessmentCategorySelectOptions = ref<CategoryOption[]>([]);
const videoCategorySelectOptions = ref<CategoryOption[]>([]);
const examOptions = ref<Exam[]>([]);
const availableExams = ref<Exam[]>([]);
const selectedExam = ref<Exam | null>(null);
const availableVideos = ref<VideoSelectOption[]>([]);
const selectedVideos = ref<VideoSelectOption[]>([]);
const availableTeachers = ref<Teacher[]>([]);
const selectedTeachers = ref<Teacher[]>([]);
const availablePracticeExams = ref<Exam[]>([]);
const selectedPracticeExams = ref<Exam[]>([]);

// 表单数据
const formData = reactive({
  title: "",
  description: "",
  category_id: undefined as number | undefined,
  exam_id: 0,
  video_ids: [] as number[],
  teacher_ids: [] as number[],
  practice_exam_ids: [] as number[],
  status: 1,
  create_makeup_exam: false,
});

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: "请输入考核标题", trigger: "blur" },
    {
      min: 1,
      max: 200,
      message: "标题长度在 1 到 200 个字符",
      trigger: "blur",
    },
  ],
  description: [
    { max: 500, message: "描述长度不能超过 500 个字符", trigger: "blur" },
  ],
  video_ids: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (
          (!value || value.length === 0) &&
          (!formData.exam_id || formData.exam_id === 0)
        ) {
          callback(new Error("至少需要选择视频或试卷中的一个"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  exam_id: [
    {
      validator: (rule: any, value: any, callback: any) => {
        // 检查是否选择了同时创建补考但没有选择试卷
        if (formData.create_makeup_exam && (!value || value === 0)) {
          callback(new Error("选择同时创建补考时，必须选择试卷"));
          return;
        }

        // 检查是否至少选择了视频或试卷
        if (
          (!value || value === 0) &&
          (!formData.video_ids || formData.video_ids.length === 0)
        ) {
          callback(new Error("至少需要选择视频或试卷中的一个"));
          return;
        }

        callback();
      },
      trigger: "change",
    },
  ],
};

// 获取考核分类树数据
const fetchAssessmentCategoryTree = async () => {
  try {
    const response = await getAssessmentCategoryTree();
    const categories = response.data || [];
    assessmentCategorySelectOptions.value =
      flattenCategoryTreeWithPath(categories);
  } catch (error) {
    console.error("获取考核分类树失败:", error);
  }
};

// 获取视频分类树数据
const fetchVideoCategoryTree = async () => {
  try {
    const response = await getCategoryTree();
    const categories = response.data || [];
    videoCategorySelectOptions.value = flattenCategoryTreeWithPath(categories);
  } catch (error) {
    console.error("获取视频分类树失败:", error);
  }
};

// 获取试卷选项
const fetchExamOptions = async () => {
  try {
    const response = await getExamList({
      page: 1,
      page_size: 100,
      status: 1,
      type: 1, // 只获取考试试卷
    });
    examOptions.value = response.data || [];
  } catch (error) {
    console.error("获取试卷列表失败:", error);
  }
};

// 获取试卷列表
const fetchExamList = async () => {
  try {
    examLoading.value = true;
    const params = {
      page: 1,
      page_size: 100,
      type: 1, // 只获取考试试卷
      status: 1, // 只获取启用状态的试卷
      ...examSearchForm,
    };
    const response = await getExamList(params);
    availableExams.value = response.data || [];
  } catch (error) {
    console.error("获取试卷列表失败:", error);
    ElMessage.error("获取试卷列表失败");
  } finally {
    examLoading.value = false;
  }
};

// 获取考核视频列表
const fetchAssessmentVideoList = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      page_size: pagination.size,
      ...searchForm,
    };
    const response = await getAssessmentVideoList(params);
    tableData.value = response.data || [];
    pagination.total = response.page.total || 0;
  } catch (error) {
    console.error("获取考核视频列表失败:", error);
    ElMessage.error("获取考核视频列表失败");
  } finally {
    loading.value = false;
  }
};

// 教师选择改变
const handleTeacherSelectionChange = (selection: Teacher[]) => {
  // 获取当前选择的教师ID
  const selectedIds = selection.map((t) => t.id);

  // 添加新选择的教师到已选列表
  selection.forEach((teacher) => {
    if (!selectedTeachers.value.find((t) => t.id === teacher.id)) {
      selectedTeachers.value.push(teacher);
    }
  });

  // 移除取消选择的教师
  selectedTeachers.value = selectedTeachers.value.filter(
    (t) =>
      selectedIds.includes(t.id) ||
      !availableTeachers.value.find((at) => at.id === t.id)
  );

  // 更新表单数据
  formData.teacher_ids = selectedTeachers.value.map((t) => t.id);
};

// 移除教师
const removeTeacher = (index: number) => {
  const removedTeacher = selectedTeachers.value[index];
  selectedTeachers.value.splice(index, 1);
  formData.teacher_ids = selectedTeachers.value.map((t) => t.id);

  // 更新表格选中状态
  if (teacherTableRef.value && removedTeacher) {
    const availableTeacher = availableTeachers.value.find(
      (t) => t.id === removedTeacher.id
    );
    if (availableTeacher) {
      teacherTableRef.value.toggleRowSelection(availableTeacher, false);
    }
  }
};

// 获取教师列表
const fetchTeacherList = async () => {
  try {
    teacherLoading.value = true;
    const params = {
      page: 1,
      page_size: 100,
      ...teacherSearchForm,
    };
    const response = await searchTeachers(params);
    availableTeachers.value = response.data || [];
  } catch (error) {
    console.error("获取教师列表失败:", error);
    ElMessage.error("获取教师列表失败");
  } finally {
    teacherLoading.value = false;
  }
};

// 搜索教师
const handleTeacherSearch = () => {
  fetchTeacherList();
};

// 获取刷题试卷列表
const fetchPracticeExamList = async () => {
  try {
    practiceExamLoading.value = true;
    const params = {
      page: 1,
      page_size: 100,
      type: 2, // 只获取刷题试卷
      status: 1, // 只获取启用状态的试卷
      ...practiceExamSearchForm,
    };
    const response = await getExamList(params);
    availablePracticeExams.value = response.data || [];
  } catch (error) {
    console.error("获取刷题试卷列表失败:", error);
    ElMessage.error("获取刷题试卷列表失败");
  } finally {
    practiceExamLoading.value = false;
  }
};

// 搜索刷题试卷
const handlePracticeExamSearch = () => {
  fetchPracticeExamList();
};

// 刷题试卷选择变化
const handlePracticeExamSelectionChange = (selection: Exam[]) => {
  selectedPracticeExams.value = selection;
  formData.practice_exam_ids = selection.map((exam) => exam.id);
};

// 移除刷题试卷
const removePracticeExam = (index: number) => {
  selectedPracticeExams.value.splice(index, 1);
  formData.practice_exam_ids = selectedPracticeExams.value.map(
    (exam) => exam.id
  );

  // 更新表格选中状态
  if (practiceExamTableRef.value) {
    practiceExamTableRef.value.clearSelection();
    selectedPracticeExams.value.forEach((selectedExam) => {
      const availableExam = availablePracticeExams.value.find(
        (e) => e.id === selectedExam.id
      );
      if (availableExam) {
        practiceExamTableRef.value.toggleRowSelection(availableExam, true);
      }
    });
  }
};

// 获取视频列表
const fetchVideoList = async () => {
  try {
    videoLoading.value = true;
    const params: VideoListRequest = {
      page: 1,
      page_size: 100,
      status: 3, // 只获取正常状态的视频
      ...videoSearchForm,
    };
    const response = await getVideoList(params);
    const videos = response.data.data || [];
    // 转换为选择选项格式
    availableVideos.value = videos.map((v: Video) => ({
      id: v.id,
      title: v.title,
      cover_image: v?.cover_image || "",
      duration: v.duration,
      category_name: v.category?.name || "",
      selected: false,
    }));
  } catch (error) {
    console.error("获取视频列表失败:", error);
    ElMessage.error("获取视频列表失败");
  } finally {
    videoLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchAssessmentVideoList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    title: "",
    category_id: undefined,
    status: undefined,
  });
  pagination.current = 1;
  fetchAssessmentVideoList();
};

// 视频搜索
const handleVideoSearch = () => {
  fetchVideoList();
};

// 试卷搜索
const handleExamSearch = () => {
  fetchExamList();
};

// 重置试卷搜索
const handleExamReset = () => {
  Object.assign(examSearchForm, {
    title: "",
  });
  fetchExamList();
};

// 试卷选择改变
const handleExamSelectionChange = (exam: Exam | null) => {
  selectedExam.value = exam;
  formData.exam_id = exam ? exam.id : 0;
};

// 清除试卷选择
const clearExamSelection = () => {
  selectedExam.value = null;
  formData.exam_id = 0;
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchAssessmentVideoList();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current;
  fetchAssessmentVideoList();
};

// 新增考核
const handleAdd = () => {
  editingId.value = null;
  resetForm();
  fetchExamList();
  fetchVideoList();
  fetchTeacherList();
  fetchPracticeExamList();
  dialogVisible.value = true;
};

// 编辑考核
const handleEdit = async (row: AssessmentVideo) => {
  editingId.value = row.id;

  try {
    // 获取详细信息
    const response = await getAssessmentVideo(row.id);
    const assessmentVideo = response.data;

    Object.assign(formData, {
      title: assessmentVideo.title,
      description: assessmentVideo.description,
      category_id: assessmentVideo.category_id,
      exam_id: assessmentVideo.exam_id,
      status: assessmentVideo.status,
    });

    // 设置已选视频
    selectedVideos.value = (assessmentVideo.videos || []).map(
      (relation: any) => ({
        id: relation.video.id,
        title: relation.video.title,
        cover_image: relation.video.cover_image,
        duration: relation.video.duration,
        category_name: relation.video.category?.name || "",
        selected: true,
      })
    );

    formData.video_ids = selectedVideos.value.map((v) => v.id);

    // 设置选中的试卷
    if (assessmentVideo.exam_id) {
      selectedExam.value = assessmentVideo.exam || null;
    }

    // 设置已选教师
    selectedTeachers.value = (assessmentVideo.teachers || [])
      .map((relation: any) => relation.teacher)
      .filter(Boolean);
    formData.teacher_ids = selectedTeachers.value.map((t) => t.id);

    // 设置已选刷题试卷
    selectedPracticeExams.value = (assessmentVideo.practice_exams || [])
      .map((practice: any) => practice.exam)
      .filter(Boolean);
    formData.practice_exam_ids = selectedPracticeExams.value.map((e) => e.id);

    await fetchExamList();
    await fetchVideoList();
    await fetchTeacherList();
    await fetchPracticeExamList();

    // 设置表格中已选视频的选中状态
    setTimeout(() => {
      if (videoTableRef.value) {
        selectedVideos.value.forEach((selectedVideo) => {
          const availableVideo = availableVideos.value.find(
            (v) => v.id === selectedVideo.id
          );
          if (availableVideo) {
            videoTableRef.value.toggleRowSelection(availableVideo, true);
          }
        });
      }

      // 设置试卷表格的当前行
      if (examTableRef.value && selectedExam.value) {
        const examRow = availableExams.value.find(
          (e) => e.id === selectedExam.value?.id
        );
        if (examRow) {
          examTableRef.value.setCurrentRow(examRow);
        }
      }

      // 设置教师表格的选中状态
      if (teacherTableRef.value) {
        selectedTeachers.value.forEach((selectedTeacher) => {
          const availableTeacher = availableTeachers.value.find(
            (t) => t.id === selectedTeacher.id
          );
          if (availableTeacher) {
            teacherTableRef.value.toggleRowSelection(availableTeacher, true);
          }
        });
      }

      // 设置刷题试卷表格的选中状态
      if (practiceExamTableRef.value) {
        selectedPracticeExams.value.forEach((selectedExam) => {
          const availableExam = availablePracticeExams.value.find(
            (e) => e.id === selectedExam.id
          );
          if (availableExam) {
            practiceExamTableRef.value.toggleRowSelection(availableExam, true);
          }
        });
      }
    }, 100);

    dialogVisible.value = true;
  } catch (error) {
    console.error("获取考核详情失败:", error);
    ElMessage.error("获取考核详情失败");
  }
};

// 删除考核
const handleDelete = async (row: AssessmentVideo) => {
  try {
    await ElMessageBox.confirm(`确定要删除考核"${row.title}"吗？`, "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await deleteAssessmentVideo(row.id);
    ElMessage.success("删除成功");
    fetchAssessmentVideoList();
  } catch (error: any) {
    if (error !== "cancel") {
      console.error("删除考核失败:", error);
      ElMessage.error(error.response?.data?.message || "删除失败");
    }
  }
};

// 视频选择改变
const handleVideoSelectionChange = (selection: VideoSelectOption[]) => {
  // 获取当前选择的视频ID
  const selectedIds = selection.map((v) => v.id);

  // 添加新选择的视频到已选列表
  selection.forEach((video) => {
    if (!selectedVideos.value.find((v) => v.id === video.id)) {
      selectedVideos.value.push({
        ...video,
        selected: true,
      });
    }
  });

  // 移除取消选择的视频
  selectedVideos.value = selectedVideos.value.filter(
    (v) =>
      selectedIds.includes(v.id) ||
      !availableVideos.value.find((av) => av.id === v.id)
  );

  // 更新表单数据
  formData.video_ids = selectedVideos.value.map((v) => v.id);
};

// 移除视频
const removeVideo = (index: number) => {
  const removedVideo = selectedVideos.value[index];
  selectedVideos.value.splice(index, 1);
  formData.video_ids = selectedVideos.value.map((v) => v.id);

  // 更新表格选中状态
  if (videoTableRef.value && removedVideo) {
    const availableVideo = availableVideos.value.find(
      (v) => v.id === removedVideo.id
    );
    if (availableVideo) {
      videoTableRef.value.toggleRowSelection(availableVideo, false);
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    if (editingId.value) {
      // 编辑模式，不处理补考创建
      await updateAssessmentVideo(editingId.value, formData);
      ElMessage.success("更新成功");
    } else {
      // 创建模式
      const { create_makeup_exam, ...mainExamData } = formData; // 移除前端特有字段

      // 创建主考核记录
      await createAssessmentVideo(mainExamData);

      // 如果选中了同时创建补考，则创建补考记录
      if (formData.create_makeup_exam && formData.exam_id) {
        const makeupExamData = {
          title: formData.title + "补考",
          description: formData.description,
          category_id: formData.category_id,
          exam_id: formData.exam_id,
          video_ids: [], // 补考不关联视频
          teacher_ids: [], // 补考不关联教师
          practice_exam_ids: [], // 补考不关联刷题试卷
          status: formData.status,
        };

        try {
          await createAssessmentVideo(makeupExamData);
          ElMessage.success("主考核和补考创建成功");
        } catch (makeupError: any) {
          console.error("创建补考失败:", makeupError);
          ElMessage.warning(
            "主考核创建成功，但补考创建失败: " +
              (makeupError.response?.data?.message || "未知错误")
          );
        }
      } else {
        ElMessage.success("创建成功");
      }
    }

    dialogVisible.value = false;
    fetchAssessmentVideoList();
  } catch (error: any) {
    console.error("提交失败:", error);
    ElMessage.error(error.response?.data?.message || "操作失败");
  } finally {
    submitting.value = false;
  }
};

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields();
  // 清除表格选中状态
  if (videoTableRef.value) {
    videoTableRef.value.clearSelection();
  }
  // 清除试卷表格当前行
  if (examTableRef.value) {
    examTableRef.value.setCurrentRow(null);
  }
  resetForm();
};

// 重置表单
const resetForm = () => {
  editingId.value = null;
  Object.assign(formData, {
    title: "",
    description: "",
    category_id: undefined,
    exam_id: 0,
    video_ids: [],
    teacher_ids: [],
    practice_exam_ids: [],
    status: 1,
    create_makeup_exam: false,
  });
  selectedVideos.value = [];
  selectedTeachers.value = [];
  selectedPracticeExams.value = [];
  selectedExam.value = null;
  Object.assign(videoSearchForm, {
    title: "",
    category_id: undefined,
  });
  Object.assign(examSearchForm, {
    title: "",
  });
  Object.assign(teacherSearchForm, {
    keyword: "",
  });
  Object.assign(practiceExamSearchForm, {
    title: "",
  });
};

// 格式化时长
const formatDuration = (duration: number) => {
  if (!duration) return "00:00";
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return "";
  return new Date(dateTime).toLocaleString("zh-CN");
};

// 页面加载时获取数据
onMounted(() => {
  fetchAssessmentCategoryTree();
  fetchVideoCategoryTree();
  fetchExamOptions();
  fetchTeacherList();
  fetchPracticeExamList();
  fetchAssessmentVideoList();
});
</script>

<style scoped>
.assessment-video-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.video-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
}

.video-search {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.video-list h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.exam-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
}

.exam-search {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.exam-list {
  margin-bottom: 20px;
}

.selected-exam {
  margin-top: 20px;
}

.selected-exam h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.exam-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exam-info {
  flex: 1;
}

.exam-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.exam-details {
  display: flex;
  gap: 15px;
  margin-bottom: 8px;
}

.exam-details span {
  color: #606266;
  font-size: 14px;
}

.exam-description {
  color: #909399;
  font-size: 14px;
  line-height: 1.4;
}

/* 抽屉样式 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
}

:deep(.el-drawer__body) {
  padding: 20px;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.form-item-tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
}
</style>
