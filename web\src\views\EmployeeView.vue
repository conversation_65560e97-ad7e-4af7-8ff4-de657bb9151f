<template>
  <div class="employee-container">
    <!-- 页面标题 -->
    <!-- 搜索表单 -->
    <el-card
      class="search-card"
      :body-style="{
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }"
    >
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="员工姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入员工姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="所属部门">
          <el-tree-select
            v-model="searchForm.department_id"
            :data="departmentTree"
            :props="{ label: 'name', value: 'wechat_id' }"
            placeholder="请选择部门"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="已激活" :value="1" />
            <el-option label="已禁用" :value="2" />
            <el-option label="未加入企业" :value="4" />
            <el-option label="退出企业" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleSearch"
            style="
              background-color: rgb(27, 179, 148);
              border-color: rgb(27, 179, 148);
            "
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <div class="cor-container">
        <el-button
          type="primary"
          style="background-color: #1bb394"
          @click="handleSyncAll"
          :loading="syncing"
        >
          <el-icon><Refresh /></el-icon>
          同步企业微信数据
        </el-button>
        <el-button @click="handleRefresh" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </el-card>

    <!-- 员工列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar
              :src="row.avatar"
              :size="40"
              style="background-color: #1bb394"
            >
              {{ row.name.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="user_id" label="企业微信ID" width="150" />
        <el-table-column prop="position" label="职位" width="150" />
        <el-table-column prop="mobile" label="手机号" width="130" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column label="所属部门" width="200">
          <template #default="{ row }">
            <el-tag
              v-for="dept in row.departments"
              :key="dept.id"
              size="small"
              style="margin-right: 5px"
            >
              {{ dept.name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status_text" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ row.status_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 员工详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="员工详情"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="currentEmployee" class="employee-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="头像">
            <el-avatar
              :src="currentEmployee.avatar"
              :size="60"
              style="background-color: #f0f0f0"
            >
              {{ currentEmployee.name.charAt(0) }}
            </el-avatar>
          </el-descriptions-item>
          <el-descriptions-item label="姓名">
            {{ currentEmployee.name }}
          </el-descriptions-item>
          <el-descriptions-item label="企业微信ID">
            {{ currentEmployee.user_id }}
          </el-descriptions-item>
          <el-descriptions-item label="职位">
            {{ currentEmployee.position || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ currentEmployee.mobile || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ currentEmployee.email || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ currentEmployee.gender || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="座机">
            {{ currentEmployee.telephone || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="别名">
            {{ currentEmployee.alias || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="地址">
            {{ currentEmployee.address || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentEmployee.status)">
              {{ currentEmployee.status_text }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="所属部门">
            <div>
              <el-tag
                v-for="dept in currentEmployee.departments"
                :key="dept.id"
                size="small"
                style="margin-right: 5px; margin-bottom: 5px"
              >
                {{ dept.name }}
              </el-tag>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Search, Refresh } from "@element-plus/icons-vue";
import {
  getEmployeeList,
  getEmployee,
  syncAll,
  getDepartmentTree,
  type Employee,
  type Department,
  type EmployeeListParams,
} from "@/api/employee";

// 响应式数据
const loading = ref(false);
const syncing = ref(false);
const dialogVisible = ref(false);
const currentEmployee = ref<Employee | null>(null);

// 搜索表单
const searchForm = reactive({
  name: "",
  department_id: undefined as number | undefined,
  status: undefined as number | undefined,
});

// 表格数据
const tableData = ref<Employee[]>([]);
const departmentTree = ref<Department[]>([]);

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 获取员工列表
const fetchEmployeeList = async () => {
  try {
    loading.value = true;
    const params: EmployeeListParams = {
      page: pagination.current,
      page_size: pagination.size,
      name: searchForm.name || undefined,
      department_id: searchForm.department_id,
      status: searchForm.status,
    };

    const response = await getEmployeeList(params);
    tableData.value = response.data || [];
    pagination.total = response.page.total || 0;
  } catch (error) {
    console.error("获取员工列表失败:", error);
    ElMessage.error("获取员工列表失败");
  } finally {
    loading.value = false;
  }
};

// 获取部门树
const fetchDepartmentTree = async () => {
  try {
    const response = await getDepartmentTree();
    departmentTree.value = response.data || [];
  } catch (error) {
    console.error("获取部门树失败:", error);
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchEmployeeList();
};

// 重置
const handleReset = () => {
  searchForm.name = "";
  searchForm.department_id = undefined;
  searchForm.status = undefined;
  pagination.current = 1;
  fetchEmployeeList();
};

// 刷新
const handleRefresh = () => {
  fetchEmployeeList();
};

// 同步企业微信数据
const handleSyncAll = async () => {
  try {
    syncing.value = true;
    await syncAll();
    ElMessage.success("同步成功");
    await fetchDepartmentTree();
    await fetchEmployeeList();
  } catch (error) {
    console.error("同步失败:", error);
    ElMessage.error("同步失败");
  } finally {
    syncing.value = false;
  }
};

// 查看员工详情
const handleView = async (row: Employee) => {
  try {
    const response = await getEmployee(row.id);
    currentEmployee.value = response.data;
    dialogVisible.value = true;
  } catch (error) {
    console.error("获取员工详情失败:", error);
    ElMessage.error("获取员工详情失败");
  }
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchEmployeeList();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current;
  fetchEmployeeList();
};

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return "success";
    case 2:
      return "danger";
    case 4:
      return "warning";
    case 5:
      return "info";
    default:
      return "primary";
  }
};

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString("zh-CN");
};

// 页面加载时获取数据
onMounted(() => {
  fetchDepartmentTree();
  fetchEmployeeList();
});
</script>

<style scoped>
.employee-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}
.search-card {
  .search-form {
    display: flex;

    align-items: center;
    justify-content: space-between;
    gap: 10px;
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.employee-detail {
  max-height: 500px;
  overflow-y: auto;
}
</style>
