-- 为试卷表添加类型字段
ALTER TABLE exams ADD COLUMN type TINYINT(1) DEFAULT 1 COMMENT '试卷类型 1考试试卷 2刷题试卷' AFTER description;

-- 更新现有试卷的类型为考试试卷
UPDATE exams SET type = 1 WHERE type IS NULL;

-- 创建考核视频与刷题试卷关联表
CREATE TABLE assessment_video_practices (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    assessment_video_id INT UNSIGNED NOT NULL COMMENT '考核视频ID',
    exam_id INT UNSIGNED NOT NULL COMMENT '刷题试卷ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    INDEX idx_assessment_video_id (assessment_video_id),
    INDEX idx_exam_id (exam_id),
    INDEX idx_deleted_at (deleted_at),
    UNIQUE KEY uk_assessment_video_exam (assessment_video_id, exam_id, deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考核视频与刷题试卷关联表';
