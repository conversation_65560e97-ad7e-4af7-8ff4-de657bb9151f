package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type QuestionController struct{}

// ListQuestions 获取题目列表
func (qc *QuestionController) ListQuestions(c *gin.Context) {
	var req models.QuestionListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.Question{}).Preload("Category")

	// 按标题搜索
	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}

	// 按类型筛选
	if req.Type != nil {
		query = query.Where("type = ?", *req.Type)
	}

	// 按难度筛选
	if req.Difficulty != nil {
		query = query.Where("difficulty = ?", *req.Difficulty)
	}

	// 按分类筛选
	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取题目总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var questions []models.Question
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("id DESC").Offset(offset).Limit(req.PageSize).Find(&questions).Error; err != nil {
		logger.Errorf("查询题目列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var responses []*models.QuestionResponse
	for _, question := range questions {
		responses = append(responses, question.ToResponse())
	}

	// 计算分页信息
	pages := int(math.Ceil(float64(total) / float64(req.PageSize)))
	pageInfo := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Total:   total,
		Pages:   pages,
	}

	utils.PageSuccess(c, responses, pageInfo)
}

// GetQuestion 获取题目详情
func (qc *QuestionController) GetQuestion(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的题目ID")
		return
	}

	var question models.Question
	if err := database.DB.Preload("Category").First(&question, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "题目不存在")
		} else {
			logger.Errorf("查询题目失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, question.ToResponse())
}

// CreateQuestion 创建题目
func (qc *QuestionController) CreateQuestion(c *gin.Context) {
	var req models.QuestionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证选项
	if len(req.Options) < 2 {
		utils.BadRequest(c, "至少需要2个选项")
		return
	}

	// 验证正确答案是否在选项中
	validAnswer := false
	for _, option := range req.Options {
		if option.Label == req.CorrectAnswer {
			validAnswer = true
			break
		}
	}
	if !validAnswer {
		utils.BadRequest(c, "正确答案必须是选项中的一个")
		return
	}

	// 如果指定了分类，检查分类是否存在
	if req.CategoryID > 0 {
		var category models.QuestionCategory
		if err := database.DB.First(&category, req.CategoryID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "题库分类不存在")
			} else {
				logger.Errorf("查询题库分类失败: %v", err)
				utils.InternalServerError(c, "系统错误")
			}
			return
		}
	}

	// 创建题目
	question := models.Question{
		Title:         req.Title,
		Content:       req.Content,
		Type:          req.Type,
		OptionsData:   req.Options,
		CorrectAnswer: req.CorrectAnswer,
		Difficulty:    req.Difficulty,
		CategoryID:    req.CategoryID,
		Status:        req.Status,
	}

	// 设置默认状态
	if question.Status == 0 {
		question.Status = 1
	}

	if err := database.DB.Create(&question).Error; err != nil {
		logger.Errorf("创建题目失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 重新查询以获取关联数据
	if err := database.DB.Preload("Category").First(&question, question.ID).Error; err != nil {
		logger.Errorf("查询新创建的题目失败: %v", err)
	}

	logger.Infof("创建题目成功: %s", question.Title)
	utils.SuccessWithMsg(c, "创建成功", question.ToResponse())
}

// UpdateQuestion 更新题目
func (qc *QuestionController) UpdateQuestion(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的题目ID")
		return
	}

	var req models.QuestionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证选项
	if len(req.Options) < 2 {
		utils.BadRequest(c, "至少需要2个选项")
		return
	}

	// 验证正确答案是否在选项中
	validAnswer := false
	for _, option := range req.Options {
		if option.Label == req.CorrectAnswer {
			validAnswer = true
			break
		}
	}
	if !validAnswer {
		utils.BadRequest(c, "正确答案必须是选项中的一个")
		return
	}

	// 查找题目
	var question models.Question
	if err := database.DB.First(&question, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "题目不存在")
		} else {
			logger.Errorf("查询题目失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 如果指定了分类，检查分类是否存在
	if req.CategoryID > 0 {
		var category models.QuestionCategory
		if err := database.DB.First(&category, req.CategoryID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "题库分类不存在")
			} else {
				logger.Errorf("查询题库分类失败: %v", err)
				utils.InternalServerError(c, "系统错误")
			}
			return
		}
	}

	// 更新题目
	question.Title = req.Title
	question.Content = req.Content
	question.Type = req.Type
	question.OptionsData = req.Options
	question.CorrectAnswer = req.CorrectAnswer
	question.Difficulty = req.Difficulty
	question.CategoryID = req.CategoryID
	question.Status = req.Status

	if err := database.DB.Save(&question).Error; err != nil {
		logger.Errorf("更新题目失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 重新查询以获取关联数据
	if err := database.DB.Preload("Category").First(&question, question.ID).Error; err != nil {
		logger.Errorf("查询更新后的题目失败: %v", err)
	}

	logger.Infof("更新题目成功: %s", question.Title)
	utils.SuccessWithMsg(c, "更新成功", question.ToResponse())
}

// DeleteQuestion 删除题目
func (qc *QuestionController) DeleteQuestion(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的题目ID")
		return
	}

	// 查找题目
	var question models.Question
	if err := database.DB.First(&question, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "题目不存在")
		} else {
			logger.Errorf("查询题目失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 检查是否有试卷使用该题目
	var examQuestionCount int64
	if err := database.DB.Model(&models.ExamQuestion{}).Where("question_id = ?", uint(id)).Count(&examQuestionCount).Error; err != nil {
		logger.Errorf("检查题目使用情况失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if examQuestionCount > 0 {
		utils.BadRequest(c, "该题目已被试卷使用，无法删除")
		return
	}

	// 软删除题目
	if err := database.DB.Delete(&question).Error; err != nil {
		logger.Errorf("删除题目失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("删除题目成功: %s", question.Title)
	utils.SuccessWithMsg(c, "删除成功", nil)
}
